2025-07-17 11:14:46,864 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:14:46,864 - INFO - 运行一次性分析...
2025-07-17 11:14:46,864 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:14:46,864 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:14:46,865 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:14:55,180 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:14:55,186 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:14:55,186 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:14:55,186 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:14:58,418 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:14:58,423 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:14:58,423 - INFO - 任务完成，耗时: 11.56秒
2025-07-17 11:14:58,423 - INFO - 一次性分析完成。
2025-07-17 11:15:30,853 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:15:30,853 - INFO - 运行一次性分析...
2025-07-17 11:15:30,853 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:15:30,853 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:15:30,853 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:15:39,669 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:15:39,674 - INFO - 中证1000 检测到信号: 空头预警信号 (强度: 2)
2025-07-17 11:15:39,674 - INFO - 为 中证1000 检测到信号，正在获取AI导师分析...
2025-07-17 11:15:39,674 - INFO - 正在调用DeepSeek Chat API...
2025-07-17 11:16:15,802 - INFO - 成功从DeepSeek Chat API获取分析。
2025-07-17 11:16:15,802 - INFO - AI导师分析结果 for 中证1000:
## 期权策略建议  
**方向**: 观察等待（需威廉指标确认）  
**行权价**: 6400（当前价格下方1.5%支撑位）  
**到期日**: 15-30天（若信号确认）  
**仓位**: 暂不建仓（信号强度2且无成交量/威廉指标确认）  

## 风险管理  
**止损**: 不适用（观察阶段）  
**止盈**: 若后续确认空头信号：  
- 第一目标6350（25%仓位止盈）  
- 第二目标6250（50%仓位止盈）  
- 剩余仓位跟踪EMA8下破止损  
**Roll Up**: 若下跌动能持续，可考虑用20%利润滚动建仓6300认沽  

## 分析依据  
1. **信号质量评估**：  
   - 当前为空头预警信号（强度2），但缺乏成交量放大（vol_ratio仅1.04）和威廉P信号确认（ZLS=0.35>0.25超买阈值）  
   - KDJ显示J值(75.65)从超买区回落，但K/D线仍金叉状态，需等待死叉确认  

2. **多空动能矛盾**：  
   - 短期EMA8(6494) > EMA21(6487) > EMA55(6474)呈现多头排列  
   - MACD柱状图负值收缩(-0.23)，DIF与DEA粘合，显示调整而非趋势反转  

3. **关键决策点**：  
   - 需等待价格有效跌破EMA21(6487)且威廉P信号触发（ZLS下穿CZX且ZLS>0.25）  
   - 当前波动率中性（vol_percentile=0.5），不适合激进建仓  

> 纪律提醒：根据"Trading With Insight"核心原则，预警信号需至少两项确认（技术形态+量能/指标）方可升级为进场信号。当前建议保持现金头寸观察。
2025-07-17 11:16:15,802 - INFO - 通知消息: --- AI导师A股期权分析: 中证1000 ---
## 期权策略建议  
**方向**: 观察等待（需威廉指标确认）  
**行权价**: 6400（当前价格下方1.5%支撑位）  
**到期日**: 15-30天（若信号确认）  
**仓位**: 暂不建仓（信号强度2且无成交量/威廉指标确认）  

## 风险管理  
**止损**: 不适用（观察阶段）  
**止盈**: 若后续确认空头信号：  
- 第一目标6350（25%仓位止盈）  
- 第二目标6250（50%仓位止盈）  
- 剩余仓位跟踪EMA8下破止损  
**Roll Up**: 若下跌动能持续，可考虑用20%利润滚动建仓6300认沽  

## 分析依据  
1. **信号质量评估**：  
   - 当前为空头预警信号（强度2），但缺乏成交量放大（vol_ratio仅1.04）和威廉P信号确认（ZLS=0.35>0.25超买阈值）  
   - KDJ显示J值(75.65)从超买区回落，但K/D线仍金叉状态，需等待死叉确认  

2. **多空动能矛盾**：  
   - 短期EMA8(6494) > EMA21(6487) > EMA55(6474)呈现多头排列  
   - MACD柱状图负值收缩(-0.23)，DIF与DEA粘合，显示调整而非趋势反转  

3. **关键决策点**：  
   - 需等待价格有效跌破EMA21(6487)且威廉P信号触发（ZLS下穿CZX且ZLS>0.25）  
   - 当前波动率中性（vol_percentile=0.5），不适合激进建仓  

> 纪律提醒：根据"Trading With Insight"核心原则，预警信号需至少两项确认（技术形态+量能/指标）方可升级为进场信号。当前建议保持现金头寸观察。
2025-07-17 11:16:15,802 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:16:15,802 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:16:19,302 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:16:19,306 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:16:19,306 - INFO - 任务完成，耗时: 48.45秒
2025-07-17 11:16:19,306 - INFO - 一次性分析完成。
2025-07-17 11:16:54,549 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:16:54,549 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 11:16:54,549 - INFO - 立即执行一次任务...
2025-07-17 11:16:54,549 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:16:54,549 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:16:54,549 - INFO - 使用缓存的中证1000数据。
2025-07-17 11:16:54,566 - INFO - 中证1000 检测到信号: 空头预警信号 (强度: 2)
2025-07-17 11:16:54,566 - INFO - 为 中证1000 检测到信号，正在获取AI导师分析...
2025-07-17 11:16:54,566 - INFO - 正在调用DeepSeek Chat API...
2025-07-17 11:17:16,648 - INFO - 成功从DeepSeek Chat API获取分析。
2025-07-17 11:17:16,649 - INFO - AI导师分析结果 for 中证1000:
## 期权策略建议  
**方向**: 观察等待（信号强度不足，需进一步确认）  
**行权价**: 暂不适用（等待信号升级）  
**到期日**: 暂不适用  
**仓位**: 0%（信号强度2且无成交量/威廉指标确认）  

## 风险管理  
**止损**: 不适用（未建仓）  
**止盈**: 不适用  
**Roll Up**: 不适用  

## 分析依据  
1. **信号质量分析**  
   - 当前为空头预警信号（强度2），但缺乏关键确认要素：  
     - 成交量比率仅1.04倍（未达3倍确认标准）  
     - 威廉指标未触发P信号（ZLS=0.35 > 0.25超买阈值）  
     - MACD柱状图收缩（-0.23），显示空头动能不足  

2. **趋势环境矛盾**  
   - 短期/中期/长期趋势均为上涨（EMA8>21>55>125）  
   - KDJ指标中J值(75.65)从超买区回落，但未形成死叉（K=81.83/D=84.92）  

3. **关键阈值监测**  
   - 需等待以下条件满足方可升级为进场信号：  
     ▶ EMA21上穿EMA8（当前EMA8=6494.30 > EMA21=6487.52）  
     ▶ 成交量突破3倍均量  
     ▶ 威廉P信号触发（ZLS>0.25且下穿CZX）  

4. **波动率环境**  
   - 隐含波动率处于中位数（vol_percentile=0.5），不支持激进方向性交易  

**建议行动**：  
- 将6490-6500设为观察区间，若价格跌破6487（EMA21）且伴随成交量放大，可重新评估  
- 警惕当前多头趋势下的逆势交易风险
2025-07-17 11:17:16,649 - INFO - 通知消息: --- AI导师A股期权分析: 中证1000 ---
## 期权策略建议  
**方向**: 观察等待（信号强度不足，需进一步确认）  
**行权价**: 暂不适用（等待信号升级）  
**到期日**: 暂不适用  
**仓位**: 0%（信号强度2且无成交量/威廉指标确认）  

## 风险管理  
**止损**: 不适用（未建仓）  
**止盈**: 不适用  
**Roll Up**: 不适用  

## 分析依据  
1. **信号质量分析**  
   - 当前为空头预警信号（强度2），但缺乏关键确认要素：  
     - 成交量比率仅1.04倍（未达3倍确认标准）  
     - 威廉指标未触发P信号（ZLS=0.35 > 0.25超买阈值）  
     - MACD柱状图收缩（-0.23），显示空头动能不足  

2. **趋势环境矛盾**  
   - 短期/中期/长期趋势均为上涨（EMA8>21>55>125）  
   - KDJ指标中J值(75.65)从超买区回落，但未形成死叉（K=81.83/D=84.92）  

3. **关键阈值监测**  
   - 需等待以下条件满足方可升级为进场信号：  
     ▶ EMA21上穿EMA8（当前EMA8=6494.30 > EMA21=6487.52）  
     ▶ 成交量突破3倍均量  
     ▶ 威廉P信号触发（ZLS>0.25且下穿CZX）  

4. **波动率环境**  
   - 隐含波动率处于中位数（vol_percentile=0.5），不支持激进方向性交易  

**建议行动**：  
- 将6490-6500设为观察区间，若价格跌破6487（EMA21）且伴随成交量放大，可重新评估  
- 警惕当前多头趋势下的逆势交易风险
2025-07-17 11:17:16,649 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:17:16,649 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 11:17:16,655 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:17:16,655 - INFO - 任务完成，耗时: 22.11秒
2025-07-17 11:17:16,655 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 11:21:41,198 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:21:41,198 - INFO - 运行一次性分析...
2025-07-17 11:21:41,198 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:21:41,198 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:21:41,198 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:21:50,580 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:21:50,580 - INFO - 从API获取csi1000日线数据...
2025-07-17 11:21:55,925 - INFO - 已缓存csi1000日线数据。
2025-07-17 11:21:55,925 - INFO - 从API获取csi1000波动率数据...
2025-07-17 11:22:00,958 - ERROR - 获取csi1000波动率数据时出错: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 11:22:00,964 - WARNING - 中证1000 未获取到VIX数据，使用计算波动率: 0.0118
2025-07-17 11:22:00,964 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:22:00,964 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:22:00,964 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:22:03,926 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:22:03,927 - INFO - 从API获取cyb_etf日线数据...
2025-07-17 11:22:09,747 - INFO - 已缓存cyb_etf日线数据。
2025-07-17 11:22:09,747 - INFO - 从API获取cyb_etf波动率数据...
2025-07-17 11:22:14,802 - ERROR - 获取cyb_etf波动率数据时出错: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 11:22:14,808 - WARNING - 创业板ETF 未获取到VIX数据，使用计算波动率: 0.0230
2025-07-17 11:22:14,808 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:22:14,809 - INFO - 任务完成，耗时: 33.61秒
2025-07-17 11:22:14,809 - INFO - 一次性分析完成。
2025-07-17 11:23:54,554 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:23:54,554 - INFO - 运行一次性分析...
2025-07-17 11:23:54,554 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:23:54,554 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:23:54,555 - INFO - 使用缓存的中证1000数据。
2025-07-17 11:23:54,558 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:23:54,559 - INFO - 从API获取csi1000波动率数据...
2025-07-17 11:23:59,682 - ERROR - 获取csi1000历史波动率失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 11:24:00,769 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 11:24:00,772 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 11:24:00,777 - INFO - 中证1000 当前VIX波动率: nan
2025-07-17 11:24:00,778 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:24:00,778 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:24:00,778 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 11:24:00,781 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:24:00,783 - INFO - 从API获取cyb_etf波动率数据...
2025-07-17 11:24:05,420 - ERROR - 获取cyb_etf历史波动率失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 11:24:06,794 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 11:24:06,796 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 11:24:06,801 - INFO - 创业板ETF 当前VIX波动率: nan
2025-07-17 11:24:06,801 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:24:06,801 - INFO - 任务完成，耗时: 12.25秒
2025-07-17 11:24:06,801 - INFO - 一次性分析完成。
2025-07-17 11:25:05,098 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:25:05,098 - INFO - 运行一次性分析...
2025-07-17 11:25:05,098 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:25:05,098 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:25:05,098 - INFO - 使用缓存的中证1000数据。
2025-07-17 11:25:05,101 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:25:05,102 - INFO - 从API获取csi1000波动率数据...
2025-07-17 11:25:12,032 - ERROR - 获取csi1000历史波动率失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 11:25:13,285 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 11:25:13,288 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 11:25:13,296 - INFO - 中证1000 当前VIX波动率: 20.87
2025-07-17 11:25:13,296 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:25:13,296 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:25:13,296 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 11:25:13,300 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:25:13,302 - INFO - 从API获取cyb_etf波动率数据...
2025-07-17 11:25:18,904 - ERROR - 获取cyb_etf历史波动率失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 11:25:20,216 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 11:25:20,217 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 11:25:20,223 - INFO - 创业板ETF 当前VIX波动率: 23.61
2025-07-17 11:25:20,224 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:25:20,224 - INFO - 任务完成，耗时: 15.13秒
2025-07-17 11:25:20,224 - INFO - 一次性分析完成。
2025-07-17 11:32:21,685 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:32:21,685 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 11:32:21,685 - INFO - 立即执行一次任务...
2025-07-17 11:32:21,685 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:32:21,685 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:32:21,685 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:32:30,764 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:32:30,764 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:32:30,766 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 11:32:30,767 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 11:32:33,585 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 11:32:33,586 - INFO - 跳过csi1000历史波动率缓存保存
2025-07-17 11:32:33,587 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 11:32:33,591 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:32:33,592 - INFO - 中证1000 VIX波动率百分位(基于技术指标): 1.00
2025-07-17 11:32:33,592 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:32:33,593 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:32:33,593 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:32:36,818 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:32:36,818 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:32:36,820 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 11:32:36,820 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 11:32:38,085 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 11:32:38,085 - INFO - 跳过cyb_etf历史波动率缓存保存
2025-07-17 11:32:38,086 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 11:32:38,095 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:32:38,096 - INFO - 创业板ETF VIX波动率百分位(基于技术指标): 1.00
2025-07-17 11:32:38,096 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:32:38,097 - INFO - 任务完成，耗时: 16.41秒
2025-07-17 11:32:38,097 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 11:37:22,202 - INFO - 当前时间 11:37:22.201827 不在交易时段
2025-07-17 11:37:22,203 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 11:40:30,791 - INFO - 收到中断信号，退出程序。
2025-07-17 11:40:32,574 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:40:32,574 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 11:40:32,574 - INFO - 立即执行一次任务...
2025-07-17 11:40:32,574 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:40:32,574 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:40:32,574 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:40:42,154 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:40:42,155 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:40:42,157 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 11:40:42,157 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 11:40:44,026 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 11:40:44,026 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 11:40:44,028 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 11:40:44,039 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:40:44,040 - INFO - 中证1000 VIX波动率百分位(基于技术指标): 100.0%
2025-07-17 11:40:44,040 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:40:44,040 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:40:44,040 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:40:47,363 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:40:47,364 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:40:47,367 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 11:40:47,367 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 11:40:48,638 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 11:40:48,638 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 11:40:48,639 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 11:40:48,642 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:40:48,643 - INFO - 创业板ETF VIX波动率百分位(基于技术指标): 100.0%
2025-07-17 11:40:48,643 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:40:48,643 - INFO - 任务完成，耗时: 16.07秒
2025-07-17 11:40:48,643 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 11:45:32,737 - INFO - 当前时间 11:45:32.737803 不在交易时段
2025-07-17 11:45:32,738 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 11:50:32,887 - INFO - 当前时间 11:50:32.887554 不在交易时段
2025-07-17 11:50:32,887 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 11:53:43,627 - INFO - 收到中断信号，退出程序。
2025-07-17 11:53:45,403 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:53:45,403 - INFO - 运行一次性分析...
2025-07-17 11:53:45,403 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:53:45,403 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:53:45,403 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:53:55,345 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:53:55,345 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:53:55,347 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 11:53:55,347 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 11:53:57,381 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 11:53:57,381 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 11:53:57,383 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 11:53:57,390 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:53:57,390 - INFO - 中证1000 VIX波动率百分位(基于技术指标): 100.0%
2025-07-17 11:53:57,391 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:53:57,391 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:53:57,391 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:54:00,712 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:54:00,712 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:54:00,714 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 11:54:00,714 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 11:54:02,068 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 11:54:02,068 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 11:54:02,069 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 11:54:02,075 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:54:02,076 - INFO - 创业板ETF VIX波动率百分位(基于技术指标): 100.0%
2025-07-17 11:54:02,076 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:54:02,076 - INFO - 任务完成，耗时: 16.67秒
2025-07-17 11:54:02,076 - INFO - 一次性分析完成。
2025-07-17 11:57:07,729 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:57:07,729 - INFO - 运行一次性分析...
2025-07-17 11:57:07,730 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:57:07,730 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:57:07,730 - INFO - 使用缓存的中证1000数据。
2025-07-17 11:57:07,733 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:57:07,734 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 11:57:07,739 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:57:07,739 - INFO - 中证1000 当前VIX: 20.99, 历史20日平均波动率: 1.27
2025-07-17 11:57:07,740 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 1646.8%
2025-07-17 11:57:07,740 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:57:07,740 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:57:07,740 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 11:57:07,742 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:57:07,743 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 11:57:07,747 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:57:07,747 - INFO - 创业板ETF 当前VIX: 23.66, 历史20日平均波动率: 2.73
2025-07-17 11:57:07,747 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 865.1%
2025-07-17 11:57:07,747 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:57:07,747 - INFO - 任务完成，耗时: 0.02秒
2025-07-17 11:57:07,747 - INFO - 一次性分析完成。
2025-07-17 11:58:01,285 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:58:01,285 - INFO - 运行一次性分析...
2025-07-17 11:58:01,285 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:58:01,285 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:58:01,285 - INFO - 使用缓存的中证1000数据。
2025-07-17 11:58:01,294 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:58:01,296 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 11:58:01,303 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:58:01,304 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率: 1.27%
2025-07-17 11:58:01,304 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 1646.8%
2025-07-17 11:58:01,304 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:58:01,304 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:58:01,304 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 11:58:01,307 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:58:01,308 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 11:58:01,312 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:58:01,313 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率: 2.73%
2025-07-17 11:58:01,313 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 865.1%
2025-07-17 11:58:01,313 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:58:01,313 - INFO - 任务完成，耗时: 0.03秒
2025-07-17 11:58:01,313 - INFO - 一次性分析完成。
2025-07-17 11:58:43,608 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:58:43,608 - INFO - 运行一次性分析...
2025-07-17 11:58:43,608 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:58:43,608 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:58:43,608 - INFO - 使用缓存的中证1000数据。
2025-07-17 11:58:43,611 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:58:43,612 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 11:58:43,617 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:58:43,617 - ERROR - 分析 中证1000 数据时出错: name 'daily_data' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 945, in job
    market_data, signal_detected = analyze_symbol_data(symbol, symbol_info)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 873, in analyze_symbol_data
    market_data, signal_detected = detect_signals(stock_data, symbol_info['name'], volatility_data)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 764, in detect_signals
    if current_vix is not None and daily_data is not None and len(daily_data) >= 60:  # 至少需要60天数据
                                   ^^^^^^^^^^
NameError: name 'daily_data' is not defined. Did you mean: 'get_daily_data'?
2025-07-17 11:58:43,620 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:58:43,620 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 11:58:43,622 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:58:43,623 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 11:58:43,626 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:58:43,626 - ERROR - 分析 创业板ETF 数据时出错: name 'daily_data' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 945, in job
    market_data, signal_detected = analyze_symbol_data(symbol, symbol_info)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 873, in analyze_symbol_data
    market_data, signal_detected = detect_signals(stock_data, symbol_info['name'], volatility_data)
                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 764, in detect_signals
    if current_vix is not None and daily_data is not None and len(daily_data) >= 60:  # 至少需要60天数据
                                   ^^^^^^^^^^
NameError: name 'daily_data' is not defined. Did you mean: 'get_daily_data'?
2025-07-17 11:58:43,628 - INFO - 任务完成，耗时: 0.02秒
2025-07-17 11:58:43,628 - INFO - 一次性分析完成。
2025-07-17 11:59:23,997 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 11:59:23,997 - INFO - 运行一次性分析...
2025-07-17 11:59:23,997 - INFO - 开始A股期权策略定时任务...
2025-07-17 11:59:23,997 - INFO - 开始分析 中证1000 数据...
2025-07-17 11:59:23,997 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 11:59:35,238 - INFO - 已更新中证1000的数据缓存。
2025-07-17 11:59:35,238 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 11:59:35,241 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 11:59:35,241 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 11:59:36,582 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 11:59:36,582 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 11:59:36,584 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 11:59:36,593 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 11:59:36,593 - WARNING - 计算中证1000历史波动率百分位失败: 'close'
2025-07-17 11:59:36,593 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 11:59:36,593 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 11:59:36,594 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 11:59:40,640 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 11:59:40,640 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 11:59:40,642 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 11:59:40,642 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 11:59:41,763 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 11:59:41,763 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 11:59:41,764 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 11:59:41,771 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 11:59:41,771 - WARNING - 计算创业板ETF历史波动率百分位失败: 'close'
2025-07-17 11:59:41,771 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 11:59:41,771 - INFO - 任务完成，耗时: 17.77秒
2025-07-17 11:59:41,771 - INFO - 一次性分析完成。
2025-07-17 12:00:58,252 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:00:58,252 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:00:58,252 - INFO - 立即执行一次任务...
2025-07-17 12:00:58,252 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:00:58,252 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:00:58,252 - INFO - 使用缓存的中证1000数据。
2025-07-17 12:00:58,258 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:00:58,260 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 12:00:58,266 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:00:58,266 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率: 14.78%
2025-07-17 12:00:58,267 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:00:58,267 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:00:58,267 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:00:58,267 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 12:00:58,269 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:00:58,270 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 12:00:58,275 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:00:58,276 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率: 19.18%
2025-07-17 12:00:58,276 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:00:58,276 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:00:58,276 - INFO - 任务完成，耗时: 0.02秒
2025-07-17 12:00:58,276 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:05:41,156 - INFO - 收到中断信号，退出程序。
2025-07-17 12:05:42,776 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:05:42,776 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:05:42,776 - INFO - 立即执行一次任务...
2025-07-17 12:05:42,776 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:05:42,776 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:05:42,776 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 12:05:53,066 - INFO - 已更新中证1000的数据缓存。
2025-07-17 12:05:53,066 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:05:53,068 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 12:05:53,068 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 12:05:54,707 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 12:05:54,708 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 12:05:54,709 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 12:05:54,715 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:05:54,716 - ERROR - 不支持的标的类型: 中证1000
2025-07-17 12:05:54,716 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:05:54,716 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:05:54,716 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:05:54,717 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:05:54,717 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:05:54,717 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 12:05:58,575 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 12:05:58,575 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:05:58,577 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 12:05:58,577 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 12:06:00,263 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 12:06:00,263 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 12:06:00,264 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 12:06:00,268 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:06:00,268 - ERROR - 不支持的标的类型: 创业板ETF
2025-07-17 12:06:00,268 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:06:00,269 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:06:00,269 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:06:00,269 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:06:00,269 - INFO - 任务完成，耗时: 17.49秒
2025-07-17 12:06:00,269 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:06:49,825 - INFO - 收到中断信号，退出程序。
2025-07-17 12:06:51,575 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:06:51,575 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:06:51,575 - INFO - 立即执行一次任务...
2025-07-17 12:06:51,575 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:06:51,575 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:06:51,575 - INFO - 使用缓存的中证1000数据。
2025-07-17 12:06:51,579 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:06:51,581 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 12:06:51,587 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:06:57,052 - ERROR - 获取csi1000历史VIX数据失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:06:57,052 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:06:57,053 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:06:57,054 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:06:57,054 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:06:57,054 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:06:57,054 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 12:06:57,058 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:06:57,061 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 12:06:57,067 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:07:00,758 - ERROR - 获取cyb_etf历史VIX数据失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:07:00,758 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:07:00,760 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:07:00,760 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:07:00,760 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:07:00,760 - INFO - 任务完成，耗时: 9.19秒
2025-07-17 12:07:00,760 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:09:06,878 - INFO - 收到中断信号，退出程序。
2025-07-17 12:09:08,616 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:09:08,617 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:09:08,617 - INFO - 立即执行一次任务...
2025-07-17 12:09:08,617 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:09:08,617 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:09:08,617 - INFO - 使用缓存的中证1000数据。
2025-07-17 12:09:08,620 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:09:08,621 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 12:09:08,626 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:09:13,822 - ERROR - 获取1000index历史VIX数据失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:09:13,822 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:09:13,823 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:09:13,823 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:09:13,823 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:09:13,823 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:09:13,823 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 12:09:13,827 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:09:13,830 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 12:09:13,835 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:09:19,125 - ERROR - 获取cyb历史VIX数据失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:09:19,125 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:09:19,126 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:09:19,127 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:09:19,127 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:09:19,127 - INFO - 任务完成，耗时: 10.51秒
2025-07-17 12:09:19,127 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:10:46,795 - INFO - 收到中断信号，退出程序。
2025-07-17 12:10:48,477 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:10:48,477 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:10:48,477 - INFO - 立即执行一次任务...
2025-07-17 12:10:48,477 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:10:48,477 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:10:48,477 - INFO - 使用缓存的中证1000数据。
2025-07-17 12:10:48,480 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:10:48,482 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 12:10:48,486 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:10:54,470 - WARNING - 中证1000 VIX数据获取失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:10:54,470 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:10:54,471 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:10:54,471 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:10:54,472 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:10:54,472 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:10:54,472 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 12:10:54,475 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:10:54,477 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 12:10:54,481 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:10:59,480 - WARNING - 创业板 VIX数据获取失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:10:59,480 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:10:59,482 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:10:59,482 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:10:59,482 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:10:59,482 - INFO - 任务完成，耗时: 11.00秒
2025-07-17 12:10:59,482 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:15:48,700 - INFO - 当前时间 12:15:48.700290 不在交易时段
2025-07-17 12:15:48,701 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 12:20:29,282 - INFO - 收到中断信号，退出程序。
2025-07-17 12:20:30,997 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:20:30,997 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:20:30,997 - INFO - 立即执行一次任务...
2025-07-17 12:20:30,997 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:20:30,997 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:20:30,997 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 12:20:40,377 - INFO - 已更新中证1000的数据缓存。
2025-07-17 12:20:40,377 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:20:40,379 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 12:20:40,379 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 12:20:41,979 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 12:20:41,979 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 12:20:41,980 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 12:20:41,987 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:20:47,926 - ERROR - 获取中证1000历史VIX数据失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:20:47,926 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:20:47,927 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:20:47,927 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:20:47,928 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:20:47,928 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:20:47,928 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 12:20:51,401 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 12:20:51,401 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:20:51,404 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 12:20:51,407 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 12:20:52,678 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 12:20:52,678 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 12:20:52,680 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 12:20:52,687 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:20:58,551 - ERROR - 获取创业板ETF历史VIX数据失败: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:20:58,551 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:20:58,552 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:20:58,552 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:20:58,552 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:20:58,552 - INFO - 任务完成，耗时: 27.55秒
2025-07-17 12:20:58,552 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:23:47,991 - INFO - 收到中断信号，退出程序。
2025-07-17 12:23:49,735 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:23:49,735 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:23:49,735 - INFO - 立即执行一次任务...
2025-07-17 12:23:49,735 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:23:49,735 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:23:49,735 - INFO - 使用缓存的中证1000数据。
2025-07-17 12:23:49,738 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:23:49,740 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 12:23:49,744 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:23:54,520 - WARNING - 获取中证1000 VIX数据时发生UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte。这通常是上游数据源编码问题，无法自动修复。程序将回退。
2025-07-17 12:23:54,520 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:23:54,521 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:23:54,521 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:23:54,522 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:23:54,522 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:23:54,522 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 12:23:54,526 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:23:54,529 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 12:23:54,534 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:24:00,228 - WARNING - 获取创业板ETF VIX数据时发生UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte。这通常是上游数据源编码问题，无法自动修复。程序将回退。
2025-07-17 12:24:00,228 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:24:00,230 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:24:00,230 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:24:00,230 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:24:00,230 - INFO - 任务完成，耗时: 10.50秒
2025-07-17 12:24:00,230 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:26:05,699 - INFO - 收到中断信号，退出程序。
2025-07-17 12:26:07,470 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:26:07,470 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:26:07,470 - INFO - 立即执行一次任务...
2025-07-17 12:26:07,470 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:26:07,470 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:26:07,470 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 12:26:16,817 - INFO - 已更新中证1000的数据缓存。
2025-07-17 12:26:16,817 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:26:16,820 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 12:26:16,820 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 12:26:19,009 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 12:26:19,009 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 12:26:19,010 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 12:26:19,015 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:26:25,573 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:26:25,577 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:26:25,579 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:26:25,579 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:26:25,580 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:26:25,580 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:26:25,580 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 12:26:30,037 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 12:26:30,037 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:26:30,040 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 12:26:30,040 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 12:26:31,597 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 12:26:31,597 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 12:26:31,598 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 12:26:31,602 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:26:37,985 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:26:37,987 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:26:37,989 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:26:37,989 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:26:37,989 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:26:37,989 - INFO - 任务完成，耗时: 30.52秒
2025-07-17 12:26:37,989 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:30:00,985 - INFO - 收到中断信号，退出程序。
2025-07-17 12:30:02,642 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 12:30:02,642 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 12:30:02,642 - INFO - 立即执行一次任务...
2025-07-17 12:30:02,642 - INFO - 开始A股期权策略定时任务...
2025-07-17 12:30:02,642 - INFO - 开始分析 中证1000 数据...
2025-07-17 12:30:02,642 - INFO - 使用缓存的中证1000数据。
2025-07-17 12:30:02,646 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 12:30:02,647 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 12:30:02,652 - INFO - 中证1000 当前VIX波动率: 20.99
2025-07-17 12:30:07,682 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:30:07,686 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 12:30:07,687 - INFO - 中证1000 当前VIX: 20.99%, 历史20日平均波动率(备选): 14.78%
2025-07-17 12:30:07,688 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 142.0%
2025-07-17 12:30:07,688 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 12:30:07,688 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 12:30:07,688 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 12:30:07,691 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 12:30:07,694 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 12:30:07,702 - INFO - 创业板ETF 当前VIX波动率: 23.66
2025-07-17 12:30:13,070 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 12:30:13,071 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 12:30:13,073 - INFO - 创业板ETF 当前VIX: 23.66%, 历史20日平均波动率(备选): 19.18%
2025-07-17 12:30:13,073 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 12:30:13,073 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 12:30:13,073 - INFO - 任务完成，耗时: 10.43秒
2025-07-17 12:30:13,073 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 12:35:03,448 - INFO - 当前时间 12:35:03.448304 不在交易时段
2025-07-17 12:35:03,449 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 12:40:03,691 - INFO - 当前时间 12:40:03.691679 不在交易时段
2025-07-17 12:40:03,693 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 12:57:25,055 - INFO - 当前时间 12:57:25.055223 不在交易时段
2025-07-17 12:57:25,055 - INFO - 当前非A股交易时段，跳过运行。
2025-07-17 13:02:25,134 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:02:25,140 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:02:25,140 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:02:34,519 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:02:34,520 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:02:34,522 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:02:34,522 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:02:36,088 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:02:36,088 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:02:36,090 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:02:36,098 - INFO - 中证1000 当前VIX波动率: 20.95
2025-07-17 13:02:41,827 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:02:41,832 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:02:41,835 - INFO - 中证1000 当前VIX: 20.95%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:02:41,835 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.7%
2025-07-17 13:02:41,835 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:02:41,835 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:02:41,836 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:02:45,089 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:02:45,089 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:02:45,091 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:02:45,091 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:02:46,724 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:02:46,724 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:02:46,726 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:02:46,736 - INFO - 创业板ETF 当前VIX波动率: 23.63
2025-07-17 13:02:50,963 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:02:50,965 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:02:50,967 - INFO - 创业板ETF 当前VIX: 23.63%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:02:50,968 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.2%
2025-07-17 13:02:50,968 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:02:50,968 - INFO - 任务完成，耗时: 25.83秒
2025-07-17 13:07:51,942 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:07:51,944 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:07:51,944 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:07:55,837 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:07:55,837 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:07:55,839 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:07:55,839 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:07:57,162 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:07:57,163 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:07:57,166 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:07:57,173 - INFO - 中证1000: 检测到成交量异常放大 (当前: 5768888, 10日均量: 3564362)
2025-07-17 13:07:57,176 - INFO - 中证1000 当前VIX波动率: 20.96
2025-07-17 13:08:02,664 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:08:02,666 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:08:02,669 - INFO - 中证1000 当前VIX: 20.96%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:08:02,669 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.8%
2025-07-17 13:08:02,670 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:08:02,670 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:08:02,670 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:08:05,730 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:08:05,730 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:08:05,732 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:08:05,732 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:08:06,838 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:08:06,838 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:08:06,840 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:08:06,848 - INFO - 创业板ETF: 检测到成交量异常放大 (当前: 561477, 10日均量: 173808)
2025-07-17 13:08:06,850 - INFO - 创业板ETF 当前VIX波动率: 23.86
2025-07-17 13:08:14,250 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:08:14,251 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:08:14,252 - INFO - 创业板ETF 当前VIX: 23.86%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:08:14,252 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 124.4%
2025-07-17 13:08:14,252 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:08:14,252 - INFO - 任务完成，耗时: 22.31秒
2025-07-17 13:13:14,318 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:13:14,319 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:13:14,319 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:13:18,183 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:13:18,183 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:13:18,186 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:13:18,186 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:13:20,683 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:13:20,684 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:13:20,686 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:13:20,697 - INFO - 中证1000 当前VIX波动率: 20.8
2025-07-17 13:13:26,577 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:13:26,579 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:13:26,582 - INFO - 中证1000 当前VIX: 20.80%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:13:26,582 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 140.7%
2025-07-17 13:13:26,582 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:13:26,583 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:13:26,583 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:13:30,324 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:13:30,324 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:13:30,327 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:13:30,327 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:13:33,300 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:13:33,300 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:13:33,302 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:13:33,311 - INFO - 创业板ETF: 检测到成交量异常放大 (当前: 349725, 10日均量: 193951)
2025-07-17 13:13:33,313 - INFO - 创业板ETF 当前VIX波动率: 23.73
2025-07-17 13:13:41,784 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:13:41,786 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:13:41,789 - INFO - 创业板ETF 当前VIX: 23.73%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:13:41,789 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.7%
2025-07-17 13:13:41,790 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:13:41,790 - INFO - 任务完成，耗时: 27.47秒
2025-07-17 13:18:41,829 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:18:41,830 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:18:41,830 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:18:44,816 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:18:44,817 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:18:44,819 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:18:44,819 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:18:45,984 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:18:45,984 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:18:45,986 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:18:45,997 - INFO - 中证1000 当前VIX波动率: 20.86
2025-07-17 13:18:50,733 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:18:50,735 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:18:50,737 - INFO - 中证1000 当前VIX: 20.86%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:18:50,738 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.1%
2025-07-17 13:18:50,738 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:18:50,738 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:18:50,738 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:18:53,159 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:18:53,160 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:18:53,161 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:18:53,161 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:18:54,027 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:18:54,028 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:18:54,030 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:18:54,040 - INFO - 创业板ETF 当前VIX波动率: 23.73
2025-07-17 13:18:58,705 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:18:58,707 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:18:58,710 - INFO - 创业板ETF 当前VIX: 23.73%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:18:58,710 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.7%
2025-07-17 13:18:58,710 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:18:58,710 - INFO - 任务完成，耗时: 16.88秒
2025-07-17 13:23:59,709 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:23:59,710 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:23:59,710 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:24:03,759 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:24:03,759 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:24:03,762 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:24:03,762 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:24:05,187 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:24:05,187 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:24:05,190 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:24:05,201 - INFO - 中证1000 当前VIX波动率: 20.87
2025-07-17 13:24:10,760 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:24:10,761 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:24:10,762 - INFO - 中证1000 当前VIX: 20.87%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:24:10,762 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.2%
2025-07-17 13:24:10,762 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:24:10,762 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:24:10,762 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:24:13,721 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:24:13,721 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:24:13,724 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:24:13,724 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:24:15,300 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:24:15,300 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:24:15,302 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:24:15,312 - INFO - 创业板ETF 当前VIX波动率: 23.68
2025-07-17 13:24:21,406 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:24:21,407 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:24:21,408 - INFO - 创业板ETF 当前VIX: 23.68%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:24:21,408 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.4%
2025-07-17 13:24:21,408 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:24:21,408 - INFO - 任务完成，耗时: 21.70秒
2025-07-17 13:29:21,514 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:29:21,515 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:29:21,515 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:29:24,833 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:29:24,833 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:29:24,835 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:29:24,835 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:29:25,964 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:29:25,964 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:29:25,966 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:29:25,977 - INFO - 中证1000 当前VIX波动率: 20.84
2025-07-17 13:29:30,681 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:29:30,684 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:29:30,687 - INFO - 中证1000 当前VIX: 20.84%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:29:30,687 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.0%
2025-07-17 13:29:30,687 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:29:30,687 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:29:30,688 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:29:33,862 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:29:33,863 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:29:33,865 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:29:33,865 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:29:35,329 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:29:35,329 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:29:35,331 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:29:35,341 - INFO - 创业板ETF 当前VIX波动率: 23.77
2025-07-17 13:29:40,701 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:29:40,703 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:29:40,706 - INFO - 创业板ETF 当前VIX: 23.77%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:29:40,706 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.9%
2025-07-17 13:29:40,706 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:29:40,706 - INFO - 任务完成，耗时: 19.19秒
2025-07-17 13:34:41,655 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:34:41,655 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:34:41,656 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:34:46,241 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:34:46,241 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:34:46,244 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:34:46,244 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:34:47,567 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:34:47,567 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:34:47,569 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:34:47,578 - INFO - 中证1000 当前VIX波动率: 20.78
2025-07-17 13:34:54,474 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:34:54,478 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:34:54,480 - INFO - 中证1000 当前VIX: 20.78%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:34:54,480 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 140.6%
2025-07-17 13:34:54,480 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:34:54,480 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:34:54,480 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:34:58,251 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:34:58,251 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:34:58,254 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:34:58,254 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:34:59,519 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:34:59,520 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:34:59,522 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:34:59,532 - INFO - 创业板ETF 当前VIX波动率: 23.81
2025-07-17 13:35:05,003 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:35:05,005 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:35:05,007 - INFO - 创业板ETF 当前VIX: 23.81%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:35:05,007 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 124.1%
2025-07-17 13:35:05,008 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:35:05,008 - INFO - 任务完成，耗时: 23.35秒
2025-07-17 13:40:05,079 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:40:05,080 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:40:05,080 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:40:08,854 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:40:08,854 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:40:08,857 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:40:08,857 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:40:10,309 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:40:10,310 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:40:10,312 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:40:10,322 - INFO - 中证1000 当前VIX波动率: 20.83
2025-07-17 13:40:17,336 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:40:17,338 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:40:17,341 - INFO - 中证1000 当前VIX: 20.83%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:40:17,341 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 140.9%
2025-07-17 13:40:17,342 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:40:17,342 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:40:17,342 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:40:21,081 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:40:21,082 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:40:21,083 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:40:21,083 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:40:23,196 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:40:23,196 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:40:23,199 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:40:23,207 - INFO - 创业板ETF: 检测到成交量异常放大 (当前: 303275, 10日均量: 220274)
2025-07-17 13:40:23,209 - INFO - 创业板ETF 当前VIX波动率: 23.8
2025-07-17 13:40:29,670 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:40:29,672 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:40:29,675 - INFO - 创业板ETF 当前VIX: 23.80%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:40:29,675 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 124.1%
2025-07-17 13:40:29,675 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:40:29,675 - INFO - 任务完成，耗时: 24.60秒
2025-07-17 13:45:30,666 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:45:30,668 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:45:30,668 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:45:34,378 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:45:34,378 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:45:34,381 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:45:34,381 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:45:35,919 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:45:35,919 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:45:35,921 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:45:35,932 - INFO - 中证1000 当前VIX波动率: 20.9
2025-07-17 13:45:41,151 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:45:41,153 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:45:41,155 - INFO - 中证1000 当前VIX: 20.90%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:45:41,155 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.4%
2025-07-17 13:45:41,156 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:45:41,156 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:45:41,157 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:45:45,160 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:45:45,160 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:45:45,162 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:45:45,162 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:45:46,528 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:45:46,528 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:45:46,531 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:45:46,542 - INFO - 创业板ETF 当前VIX波动率: 23.77
2025-07-17 13:45:52,547 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:45:52,549 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:45:52,550 - INFO - 创业板ETF 当前VIX: 23.77%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:45:52,550 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.9%
2025-07-17 13:45:52,550 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:45:52,550 - INFO - 任务完成，耗时: 21.88秒
2025-07-17 13:50:52,609 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:50:52,611 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:50:52,611 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:50:56,023 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:50:56,023 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:50:56,025 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:50:56,025 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:50:57,134 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:50:57,135 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:50:57,135 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:50:57,139 - INFO - 中证1000 当前VIX波动率: 20.87
2025-07-17 13:51:01,976 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:51:01,979 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:51:01,981 - INFO - 中证1000 当前VIX: 20.87%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:51:01,981 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.2%
2025-07-17 13:51:01,982 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:51:01,982 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:51:01,982 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:51:05,139 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:51:05,140 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:51:05,142 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:51:05,142 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:51:06,571 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:51:06,571 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:51:06,572 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:51:06,580 - INFO - 创业板ETF 当前VIX波动率: 23.77
2025-07-17 13:51:11,999 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:51:12,000 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:51:12,001 - INFO - 创业板ETF 当前VIX: 23.77%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:51:12,002 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.9%
2025-07-17 13:51:12,002 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:51:12,002 - INFO - 任务完成，耗时: 19.39秒
2025-07-17 13:56:12,068 - INFO - 开始A股期权策略定时任务...
2025-07-17 13:56:12,070 - INFO - 开始分析 中证1000 数据...
2025-07-17 13:56:12,070 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 13:56:15,433 - INFO - 已更新中证1000的数据缓存。
2025-07-17 13:56:15,433 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 13:56:15,435 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 13:56:15,435 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 13:56:17,198 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 13:56:17,199 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:56:17,201 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 13:56:17,213 - INFO - 中证1000 当前VIX波动率: 20.84
2025-07-17 13:56:22,455 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:56:22,456 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 13:56:22,456 - INFO - 中证1000 当前VIX: 20.84%, 历史20日平均波动率(备选): 14.78%
2025-07-17 13:56:22,456 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.0%
2025-07-17 13:56:22,456 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 13:56:22,456 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 13:56:22,456 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 13:56:25,492 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 13:56:25,493 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 13:56:25,495 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 13:56:25,495 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 13:56:26,573 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 13:56:26,574 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 13:56:26,575 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 13:56:26,580 - INFO - 创业板ETF 当前VIX波动率: 23.72
2025-07-17 13:56:33,542 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 13:56:33,547 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 13:56:33,548 - INFO - 创业板ETF 当前VIX: 23.72%, 历史20日平均波动率(备选): 19.18%
2025-07-17 13:56:33,548 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.6%
2025-07-17 13:56:33,549 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 13:56:33,549 - INFO - 任务完成，耗时: 21.48秒
2025-07-17 14:01:34,132 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:01:34,134 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:01:34,134 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 14:01:37,243 - INFO - 已更新中证1000的数据缓存。
2025-07-17 14:01:37,243 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 14:01:37,245 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 14:01:37,246 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 14:01:38,747 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 14:01:38,747 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:01:38,748 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 14:01:38,756 - INFO - 中证1000 当前VIX波动率: 20.85
2025-07-17 14:01:45,874 - ERROR - 获取 中证1000 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 545, in get_historical_vix_data
    vix_df = ak.index_option_1000index_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 308, in index_option_1000index_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 25, 26, 27, 28]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 14:01:45,876 - WARNING - 中证1000 无法获取历史VIX数据
2025-07-17 14:01:45,878 - INFO - 中证1000 当前VIX: 20.85%, 历史20日平均波动率(备选): 14.78%
2025-07-17 14:01:45,878 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 141.1%
2025-07-17 14:01:45,878 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 14:01:45,878 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 14:01:45,878 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 14:01:48,950 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 14:01:48,950 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 14:01:48,953 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 14:01:48,953 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 14:01:50,875 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 14:01:50,875 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:01:50,877 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 14:01:50,884 - INFO - 创业板ETF: 威廉指标P信号 - 超买回调 (ZLS=0.332)
2025-07-17 14:01:50,885 - INFO - 创业板ETF 当前VIX波动率: 23.67
2025-07-17 14:01:57,143 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 547, in get_historical_vix_data
    vix_df = ak.index_option_cyb_qvix().rename(columns={'close': 'vix_close'})
             ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/akshare/index/index_option_qvix.py", line 144, in index_option_cyb_qvix
    temp_df = pd.read_csv(url).iloc[:, [0, 71, 72, 73, 74]]
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1026, in read_csv
    return _read(filepath_or_buffer, kwds)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 626, in _read
    return parser.read(nrows)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/readers.py", line 1923, in read
    ) = self._engine.read(  # type: ignore[attr-defined]
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/opt/anaconda3/lib/python3.12/site-packages/pandas/io/parsers/c_parser_wrapper.py", line 234, in read
    chunks = self._reader.read_low_memory(nrows)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "parsers.pyx", line 838, in pandas._libs.parsers.TextReader.read_low_memory
  File "parsers.pyx", line 905, in pandas._libs.parsers.TextReader._read_rows
  File "parsers.pyx", line 874, in pandas._libs.parsers.TextReader._tokenize_rows
  File "parsers.pyx", line 891, in pandas._libs.parsers.TextReader._check_tokenize_status
  File "parsers.pyx", line 2053, in pandas._libs.parsers.raise_parser_error
  File "<frozen codecs>", line 322, in decode
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa1 in position 147331: invalid start byte
2025-07-17 14:01:57,145 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 14:01:57,148 - INFO - 创业板ETF 当前VIX: 23.67%, 历史20日平均波动率(备选): 19.18%
2025-07-17 14:01:57,148 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.4%
2025-07-17 14:01:57,149 - INFO - 创业板ETF 检测到信号: 威廉超买回调信号 (强度: 2) (纯威廉P信号)
2025-07-17 14:01:57,149 - INFO - 为 创业板ETF 检测到信号，正在获取AI导师分析...
2025-07-17 14:01:57,150 - INFO - 正在调用DeepSeek Chat API...
2025-07-17 14:02:22,561 - INFO - 成功从DeepSeek Chat API获取分析。
2025-07-17 14:02:22,563 - INFO - AI导师分析结果 for 创业板ETF:
## 期权策略建议  
**方向**: 买入看跌期权（短期回调交易）  
**行权价**: 2.20（接近EMA55支撑位）  
**到期日**: 10-15天（覆盖短期回调窗口）  
**仓位**: 1%（信号强度2+纯威廉P信号，但缺乏成交量确认）  

## 风险管理  
**止损**: 30%期权权利金损失或EMA8重新上穿EMA21  
**止盈**: 分层策略：  
- 第一目标2.18（25%仓位）  
- 第二目标2.15（50%仓位）  
- 剩余仓位追踪至趋势反转信号  
**Roll Up**: 若下跌动能持续，用20%利润买入更低行权价(2.15)的次月合约  

## 分析依据  
1. **信号质量分析**：  
   - 纯威廉P信号（ZLS=0.33>0.25阈值）显示超买回调压力  
   - KDJ指标中J值(64.58)从超买区回落，但K/D尚未死叉  
   - 不足：成交量仅达10日均量41%，需警惕动能不足  

2. **多时间框架验证**：  
   - 短期趋势仍为上涨（EMA8>21>55），但MACD柱状体连续收缩(-0.000046)  
   - 关键阻力：2.25（前高心理关口），支撑：2.22（EMA21）、2.20（EMA55）  

3. **波动率环境**：  
   - VIX处于123.4%历史分位，高波动环境放大双向风险  
   - 建议选择轻度虚值期权（Delta≈0.3）平衡成本与赔率  

4. **风险回报比**：  
   - 下行空间：2.20(-1.8%)→2.15(-4%)  
   - 上行风险：突破2.25需止损  
   - 符合2:1最小要求  

> 注意：此为逆短期趋势交易，需严格止损。若14:30前无放量跌破2.23，则放弃入场。
2025-07-17 14:02:22,563 - INFO - 通知消息: --- AI导师A股期权分析: 创业板ETF ---
## 期权策略建议  
**方向**: 买入看跌期权（短期回调交易）  
**行权价**: 2.20（接近EMA55支撑位）  
**到期日**: 10-15天（覆盖短期回调窗口）  
**仓位**: 1%（信号强度2+纯威廉P信号，但缺乏成交量确认）  

## 风险管理  
**止损**: 30%期权权利金损失或EMA8重新上穿EMA21  
**止盈**: 分层策略：  
- 第一目标2.18（25%仓位）  
- 第二目标2.15（50%仓位）  
- 剩余仓位追踪至趋势反转信号  
**Roll Up**: 若下跌动能持续，用20%利润买入更低行权价(2.15)的次月合约  

## 分析依据  
1. **信号质量分析**：  
   - 纯威廉P信号（ZLS=0.33>0.25阈值）显示超买回调压力  
   - KDJ指标中J值(64.58)从超买区回落，但K/D尚未死叉  
   - 不足：成交量仅达10日均量41%，需警惕动能不足  

2. **多时间框架验证**：  
   - 短期趋势仍为上涨（EMA8>21>55），但MACD柱状体连续收缩(-0.000046)  
   - 关键阻力：2.25（前高心理关口），支撑：2.22（EMA21）、2.20（EMA55）  

3. **波动率环境**：  
   - VIX处于123.4%历史分位，高波动环境放大双向风险  
   - 建议选择轻度虚值期权（Delta≈0.3）平衡成本与赔率  

4. **风险回报比**：  
   - 下行空间：2.20(-1.8%)→2.15(-4%)  
   - 上行风险：突破2.25需止损  
   - 符合2:1最小要求  

> 注意：此为逆短期趋势交易，需严格止损。若14:30前无放量跌破2.23，则放弃入场。
2025-07-17 14:02:22,563 - INFO - 任务完成，耗时: 48.43秒
2025-07-17 14:07:23,268 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:07:23,269 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:07:23,270 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 14:07:26,165 - INFO - 已更新中证1000的数据缓存。
2025-07-17 14:07:26,165 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 14:07:26,168 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 14:07:26,168 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 14:07:32,914 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 14:07:32,915 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 14:07:32,915 - INFO - 立即执行一次任务...
2025-07-17 14:07:32,915 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:07:32,915 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:07:32,915 - INFO - 使用缓存的中证1000数据。
2025-07-17 14:07:32,921 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 14:07:32,923 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 14:07:32,923 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 14:07:34,431 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 14:07:34,432 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:07:34,437 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 14:07:34,448 - INFO - 中证1000 当前VIX波动率: 20.85
2025-07-17 14:07:40,098 - INFO - 成功获取 中证1000 历史VIX数据，共2528条记录
2025-07-17 14:07:40,100 - INFO - 中证1000 当前VIX: 20.85%, 历史20日平均VIX: 20.74%
2025-07-17 14:07:40,100 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 100.5%
2025-07-17 14:07:40,100 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 14:07:40,100 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 14:07:40,100 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 14:08:08,010 - ERROR - 获取创业板ETF数据时出错: HTTPSConnectionPool(host='80.push2.eastmoney.com', port=443): Read timed out. (read timeout=15)
2025-07-17 14:08:08,011 - WARNING - API请求失败，使用创业板ETF的旧缓存数据。
2025-07-17 14:08:08,022 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 14:08:08,028 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 14:08:08,028 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 14:08:09,452 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 14:08:09,452 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:08:09,455 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 14:08:09,466 - INFO - 创业板ETF: 威廉指标P信号 - 超买回调 (ZLS=0.332)
2025-07-17 14:08:09,469 - INFO - 创业板ETF 当前VIX波动率: 23.7
2025-07-17 14:08:09,475 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: Can only use .str accessor with string values!
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 549, in get_historical_vix_data
    vix_df = vix_df.apply(lambda x: x.str.decode('gb2312', errors='ignore') if x.dtype == 'object' else x)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/frame.py", line 10381, in apply
    return op.apply().__finalize__(self, method="apply")
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py", line 916, in apply
    return self.apply_standard()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py", line 1063, in apply_standard
    results, res_index = self.apply_series_generator()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py", line 1081, in apply_series_generator
    results[i] = self.func(v, *self.args, **self.kwargs)
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 549, in <lambda>
    vix_df = vix_df.apply(lambda x: x.str.decode('gb2312', errors='ignore') if x.dtype == 'object' else x)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/generic.py", line 6318, in __getattr__
    return object.__getattribute__(self, name)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/accessor.py", line 224, in __get__
    accessor_obj = self._accessor(obj)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py", line 194, in __init__
    self._inferred_dtype = self._validate(data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py", line 248, in _validate
    raise AttributeError("Can only use .str accessor with string values!")
AttributeError: Can only use .str accessor with string values!
2025-07-17 14:08:09,488 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 14:08:09,489 - INFO - 创业板ETF 当前VIX: 23.70%, 历史20日平均波动率(备选): 19.18%
2025-07-17 14:08:09,489 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.5%
2025-07-17 14:08:09,489 - INFO - 创业板ETF 检测到信号: 威廉超买回调信号 (强度: 2) (纯威廉P信号)
2025-07-17 14:08:09,489 - INFO - 为 创业板ETF 检测到信号，正在获取AI导师分析...
2025-07-17 14:08:09,489 - INFO - 正在调用DeepSeek Chat API...
2025-07-17 14:08:28,456 - INFO - 成功从DeepSeek Chat API获取分析。
2025-07-17 14:08:28,457 - INFO - AI导师分析结果 for 创业板ETF:
## 期权策略建议  
**方向**: 买入短期看跌期权 (Buy Short-term Puts)  
**行权价**: 2.20（虚值约1.8%）  
**到期日**: 7-15天（捕捉短期回调）  
**仓位**: 1%（信号强度2且无成交量确认）  

## 风险管理  
**止损**: 30%期权权利金损失或EMA8上穿EMA21  
**止盈**: 分两档（50%仓位在2.15支撑位，剩余50%在2.10心理关口）  
**Roll Up**: 若首档止盈触发，用10%利润买入2.15行权价新PUT  

## 分析依据  
1. **信号质量**：纯威廉P信号（ZLS=0.33>0.25超买阈值），但强度仅2级（缺成交量确认）  
2. **技术矛盾**：  
   - 多EMA多头排列（8>21>55>125）与短期超卖信号冲突  
   - KDJ死叉初现（K=77.9下穿D=84.6），但J值未破80  
3. **波动率环境**：VIX处于123.5%分位，适合方向性交易但需严控仓位  
4. **关键支撑**：2.20为近期成交密集区，2.15为EMA55动态支撑  

> 注意：当前处于"上涨趋势中的回调信号"，需警惕趋势延续风险。建议仅用1%仓位试单，若突破2.25前高则立即止损。
2025-07-17 14:08:28,458 - INFO - 通知消息: --- AI导师A股期权分析: 创业板ETF ---
## 期权策略建议  
**方向**: 买入短期看跌期权 (Buy Short-term Puts)  
**行权价**: 2.20（虚值约1.8%）  
**到期日**: 7-15天（捕捉短期回调）  
**仓位**: 1%（信号强度2且无成交量确认）  

## 风险管理  
**止损**: 30%期权权利金损失或EMA8上穿EMA21  
**止盈**: 分两档（50%仓位在2.15支撑位，剩余50%在2.10心理关口）  
**Roll Up**: 若首档止盈触发，用10%利润买入2.15行权价新PUT  

## 分析依据  
1. **信号质量**：纯威廉P信号（ZLS=0.33>0.25超买阈值），但强度仅2级（缺成交量确认）  
2. **技术矛盾**：  
   - 多EMA多头排列（8>21>55>125）与短期超卖信号冲突  
   - KDJ死叉初现（K=77.9下穿D=84.6），但J值未破80  
3. **波动率环境**：VIX处于123.5%分位，适合方向性交易但需严控仓位  
4. **关键支撑**：2.20为近期成交密集区，2.15为EMA55动态支撑  

> 注意：当前处于"上涨趋势中的回调信号"，需警惕趋势延续风险。建议仅用1%仓位试单，若突破2.25前高则立即止损。
2025-07-17 14:08:28,458 - INFO - 任务完成，耗时: 55.54秒
2025-07-17 14:08:28,458 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 14:12:33,411 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:12:33,412 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:12:33,412 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 14:13:21,087 - INFO - 已更新中证1000的数据缓存。
2025-07-17 14:13:21,088 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 14:13:21,090 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 14:13:21,090 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 14:13:22,728 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 14:13:22,728 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:13:22,729 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 14:13:22,735 - INFO - 中证1000 当前VIX波动率: 20.84
2025-07-17 14:13:22,739 - INFO - 成功获取 中证1000 历史VIX数据，共2528条记录
2025-07-17 14:13:22,740 - INFO - 中证1000 当前VIX: 20.84%, 历史20日平均VIX: 20.74%
2025-07-17 14:13:22,740 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 100.5%
2025-07-17 14:13:22,740 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 14:13:22,740 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 14:13:22,740 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 14:13:28,392 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 14:13:28,393 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 14:13:28,395 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 14:13:28,395 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 14:13:29,971 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 14:13:29,971 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:13:29,973 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 14:13:29,983 - INFO - 创业板ETF 当前VIX波动率: 23.65
2025-07-17 14:13:29,987 - ERROR - 获取 创业板ETF 历史VIX数据时发生严重错误: Can only use .str accessor with string values!
Traceback (most recent call last):
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 549, in get_historical_vix_data
    text_columns = vix_df.select_dtypes(include='object').columns
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/frame.py", line 10381, in apply
    return op.apply().__finalize__(self, method="apply")
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py", line 916, in apply
    return self.apply_standard()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py", line 1063, in apply_standard
    results, res_index = self.apply_series_generator()
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/apply.py", line 1081, in apply_series_generator
    results[i] = self.func(v, *self.args, **self.kwargs)
  File "/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/gamma_shock_cn/gamma_shock_csi.py", line 549, in <lambda>
    text_columns = vix_df.select_dtypes(include='object').columns
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/generic.py", line 6318, in __getattr__
    return object.__getattribute__(self, name)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/accessor.py", line 224, in __get__
    accessor_obj = self._accessor(obj)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py", line 194, in __init__
    self._inferred_dtype = self._validate(data)
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/pandas/core/strings/accessor.py", line 248, in _validate
    raise AttributeError("Can only use .str accessor with string values!")
AttributeError: Can only use .str accessor with string values!
2025-07-17 14:13:29,990 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 14:13:29,991 - INFO - 创业板ETF 当前VIX: 23.65%, 历史20日平均波动率(备选): 19.18%
2025-07-17 14:13:29,991 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 123.3%
2025-07-17 14:13:29,991 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 14:13:29,991 - INFO - 任务完成，耗时: 56.58秒
2025-07-17 14:16:25,568 - INFO - 收到中断信号，退出程序。
2025-07-17 14:16:26,212 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 14:16:26,212 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 14:16:26,212 - INFO - 立即执行一次任务...
2025-07-17 14:16:26,212 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:16:26,212 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:16:26,212 - INFO - 使用缓存的中证1000数据。
2025-07-17 14:16:26,216 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 14:16:26,218 - INFO - 使用缓存的csi1000分时波动率数据。
2025-07-17 14:16:26,235 - INFO - 中证1000 当前VIX波动率: 20.84
2025-07-17 14:16:33,143 - INFO - 成功获取 中证1000 历史VIX数据，共2528条记录
2025-07-17 14:16:33,144 - INFO - 中证1000 当前VIX: 20.84%, 历史20日平均VIX: 20.74%
2025-07-17 14:16:33,145 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 100.5%
2025-07-17 14:16:33,145 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 14:16:33,145 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 14:16:33,145 - INFO - 使用缓存的创业板ETF数据。
2025-07-17 14:16:33,148 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 14:16:33,150 - INFO - 使用缓存的cyb_etf分时波动率数据。
2025-07-17 14:16:33,155 - INFO - 创业板ETF 当前VIX波动率: 23.65
2025-07-17 14:16:33,158 - WARNING - 列 date 解码失败: Can only use .str accessor with string values!
2025-07-17 14:16:33,160 - WARNING - 列 open 解码失败: Can only use .str accessor with string values!
2025-07-17 14:16:33,160 - WARNING - 列 high 解码失败: Can only use .str accessor with string values!
2025-07-17 14:16:33,161 - WARNING - 列 low 解码失败: Can only use .str accessor with string values!
2025-07-17 14:16:33,161 - WARNING - 列 vix_close 解码失败: Can only use .str accessor with string values!
2025-07-17 14:16:33,162 - INFO - 成功获取 创业板ETF 历史VIX数据，共2528条记录
2025-07-17 14:16:33,163 - WARNING - 计算创业板ETF历史VIX百分位失败: Could not convert string '22.6121.5520.3321.1426.7724.0523.2223.8322.9521.8322.017.6822.924.3624.5424.7725.6825.624.9623.84' to numeric
2025-07-17 14:16:33,163 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 14:16:33,163 - INFO - 任务完成，耗时: 6.95秒
2025-07-17 14:16:33,163 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 14:21:15,550 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 14:21:15,550 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 14:21:15,550 - INFO - 立即执行一次任务...
2025-07-17 14:21:15,550 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:21:15,550 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:21:15,550 - INFO - 从API获取中证1000分钟线数据...
2025-07-17 14:22:00,754 - INFO - 已更新中证1000的数据缓存。
2025-07-17 14:22:00,755 - INFO - 使用缓存的csi1000日线数据。
2025-07-17 14:22:00,757 - INFO - 从API获取csi1000分时波动率数据...
2025-07-17 14:22:00,757 - INFO - 跳过csi1000历史波动率获取，将使用技术指标计算
2025-07-17 14:22:02,422 - INFO - 成功获取csi1000分时波动率数据，形状: (239, 2)
2025-07-17 14:22:02,423 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:22:02,426 - INFO - 已缓存csi1000分时波动率数据。
2025-07-17 14:22:02,439 - INFO - 中证1000 当前VIX波动率: 20.82
2025-07-17 14:22:08,546 - INFO - 成功获取 中证1000 历史VIX数据，共2528条记录
2025-07-17 14:22:08,551 - INFO - 中证1000 当前VIX: 20.82%, 历史20日平均VIX: 20.74%
2025-07-17 14:22:08,551 - INFO - 中证1000 VIX波动率百分位(当前/历史平均): 100.4%
2025-07-17 14:22:08,551 - INFO - 中证1000 未检测到明确交易信号。
2025-07-17 14:22:08,551 - INFO - 开始分析 创业板ETF 数据...
2025-07-17 14:22:08,552 - INFO - 从API获取创业板ETF分钟线数据...
2025-07-17 14:22:14,770 - INFO - 已更新创业板ETF的数据缓存。
2025-07-17 14:22:14,770 - INFO - 使用缓存的cyb_etf日线数据。
2025-07-17 14:22:14,772 - INFO - 从API获取cyb_etf分时波动率数据...
2025-07-17 14:22:14,772 - INFO - 跳过cyb_etf历史波动率获取，将使用技术指标计算
2025-07-17 14:22:16,640 - INFO - 成功获取cyb_etf分时波动率数据，形状: (239, 2)
2025-07-17 14:22:16,640 - INFO - 历史波动率将使用技术指标计算，无需缓存
2025-07-17 14:22:16,642 - INFO - 已缓存cyb_etf分时波动率数据。
2025-07-17 14:22:16,656 - INFO - 创业板ETF 当前VIX波动率: 23.78
2025-07-17 14:22:16,670 - WARNING - 列 date 清洗失败: Can only use .str accessor with string values!
2025-07-17 14:22:16,671 - ERROR - 创业板ETF VIX数据无效值超过30%，请检查数据源
2025-07-17 14:22:16,671 - WARNING - 创业板ETF 无法获取历史VIX数据
2025-07-17 14:22:16,672 - INFO - 创业板ETF 当前VIX: 23.78%, 历史20日平均波动率(备选): 19.18%
2025-07-17 14:22:16,673 - INFO - 创业板ETF VIX波动率百分位(当前/历史平均): 124.0%
2025-07-17 14:22:16,673 - INFO - 创业板ETF 未检测到明确交易信号。
2025-07-17 14:22:16,673 - INFO - 任务完成，耗时: 61.12秒
2025-07-17 14:22:16,673 - INFO - 定时任务已设置，每5分钟运行一次。
2025-07-17 14:32:42,604 - INFO - 成功加载AI导师配置文件: options_trading_prompt.json
2025-07-17 14:32:42,604 - INFO - 启动A股期权策略定时任务调度器...
2025-07-17 14:32:42,604 - INFO - 立即执行一次任务...
2025-07-17 14:32:42,604 - INFO - 开始A股期权策略定时任务...
2025-07-17 14:32:42,604 - INFO - 开始分析 中证1000 数据...
2025-07-17 14:32:42,605 - INFO - 从API获取中证1000分钟线数据...
