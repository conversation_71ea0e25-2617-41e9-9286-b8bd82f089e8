# -*- coding: utf-8 -*-
"""
A股期权策略跟踪系统
- 应用于中证1000和创业板ETF标的
- 基于双重确认升级版策略
- 利用AI导师(DeepSeek)进行交易信号分析
"""
import akshare as ak
import pandas as pd
from functools import lru_cache
from datetime import datetime, timedelta
import os
import sys
import json
import time
import requests
import schedule
import logging
import argparse
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
import numpy as np
from dotenv import load_dotenv

# --- AI导师模块 ---
AGENT_PROMPT_JSON_PATH = "options_trading_prompt.json"
MASTER_AGENT_PROMPT = None

def load_master_agent_prompt():
    """
    /**
     * @description 在脚本启动时加载AI导师的配置文件
     */
    """
    global MASTER_AGENT_PROMPT
    try:
        with open(AGENT_PROMPT_JSON_PATH, 'r', encoding='utf-8') as f:
            MASTER_AGENT_PROMPT = json.load(f)
        logger.info(f"成功加载AI导师配置文件: {AGENT_PROMPT_JSON_PATH}")
    except Exception as e:
        logger.error(f"加载AI导师配置文件失败: {e}")
        MASTER_AGENT_PROMPT = None

def call_llm_api(system_prompt: str, user_prompt: str) -> str:
    """
    /**
     * @description 调用DeepSeek大语言模型API的函数。
     * @param {string} system_prompt - 系统提示词，定义了AI的角色和规则。
     * @param {string} user_prompt - 用户提示词，包含了实时市场数据和请求。
     * @returns {string} 从LLM返回的分析文本。
     */
    """
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        logger.error("错误: DEEPSEEK_API_KEY 环境变量未设置。")
        return "错误: DeepSeek API Key未配置。"

    url = "https://api.deepseek.com/chat/completions"
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    }
    
    payload = {
        "model": "deepseek-chat",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        "temperature": 0.7,
        "stream": False
    }

    try:
        logger.info("正在调用DeepSeek Chat API...")
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        
        data = response.json()
        content = data['choices'][0]['message']['content']
        logger.info("成功从DeepSeek Chat API获取分析。")
        return content

    except requests.exceptions.RequestException as e:
        logger.error(f"调用DeepSeek API时发生网络错误: {e}")
        return f"错误: 调用DeepSeek API时发生网络错误: {e}"
    except KeyError:
        logger.error(f"从DeepSeek API返回的数据格式不正确: {response.text}")
        return "错误: 从DeepSeek API返回的数据格式不正确。"
    except Exception as e:
        logger.error(f"调用DeepSeek API时发生未知错误: {e}")
        return f"错误: 调用DeepSeek API时发生未知错误: {e}"

def get_master_analysis(market_data: dict) -> str:
    """
    /**
     * @description 获取AI导师的综合分析。
     * @param {dict} market_data - 包含市场数据的结构化字典。
     * @returns {string} AI导师的详细分析。
     */
    """
    if not MASTER_AGENT_PROMPT:
        return "错误: AI导师的配置文件未加载。"

    try:
        system_prompt = json.dumps(MASTER_AGENT_PROMPT, ensure_ascii=False)
        user_prompt_text = f"""你好，Alata。我的A股期权双重确认策略脚本检测到一个潜在的交易信号，请按照结论优先的格式进行分析。

请严格按照以下格式回复：

## 期权策略建议
**方向**: [买入看涨期权/买入看跌期权/观察等待]
**行权价**: [具体价格]（[价格相对性描述]）
**到期日**: [建议天数范围]
**仓位**: [百分比]（[基于信号强度的说明]）

## 风险管理
**止损**: [具体条件或百分比]
**止盈**: [分层策略描述]
**Roll Up**: [滚动策略说明]

## 分析依据
[详细的技术分析和市场逻辑]

以下是相关数据：
{json.dumps(market_data, indent=2, ensure_ascii=False)}"""
        
        analysis = call_llm_api(system_prompt, user_prompt_text)
        return analysis
        
    except Exception as e:
        logger.error(f"获取AI导师分析时出错: {e}")
        return f"获取AI导师分析时出错: {e}"

# --- 配置 ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "gamma_shock_csi.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

CONFIG = {
    'data_dir': os.path.join(os.path.dirname(__file__), "data"),
    'symbols': {
        'csi1000': {'code': '399852', 'name': '中证1000'},
        'cyb_etf': {'code': '159915', 'name': '创业板ETF'}
    },
    'period': "5"  # 单位：分钟
}

data_dir = str(CONFIG['data_dir'])
cache_dir = os.path.join(data_dir, "cache")

# 创建数据目录
if not os.path.exists(data_dir):
    os.makedirs(data_dir)
if not os.path.exists(cache_dir):
    os.makedirs(cache_dir)

# --- 数据获取模块 ---

def get_csi1000_data(period: str = "5"):
    """
    /**
     * @description 获取中证1000指数分钟线数据，并进行缓存和处理。
     * @param {string} period - K线周期（分钟）。
     * @returns {pd.DataFrame | None} 处理后的分钟线数据。
     */
    """
    cache_file = os.path.join(cache_dir, f"csi1000_{period}min.csv")
    
    # 尝试从缓存加载
    if os.path.exists(cache_file):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        # 对于分钟线数据，设置一个较短的缓存有效期
        if (datetime.now() - file_mod_time).seconds < int(period) * 60:
            try:
                logger.info("使用缓存的中证1000数据。")
                df = pd.read_csv(cache_file)
                df['时间'] = pd.to_datetime(df['时间'])
                return df.set_index('时间')
            except Exception as e:
                logger.warning(f"读取中证1000缓存失败: {e}, 将重新获取。")

    logger.info("从API获取中证1000分钟线数据...")
    try:
        # 计算日期范围
        end_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取中证1000分钟线数据
        index_data = ak.index_zh_a_hist_min_em(
            symbol="399852", 
            period=period, 
            start_date=start_date, 
            end_date=end_date
        )
        
        if index_data is None or index_data.empty:
            logger.warning("未能获取到中证1000的分钟线数据。")
            return None

        # 数据预处理
        index_data['时间'] = pd.to_datetime(index_data['时间'])
        index_data.set_index('时间', inplace=True)
        
        # 将数据类型转换为数值型
        for col in ['开盘', '最高', '最低', '收盘', '成交量']:
            index_data[col] = pd.to_numeric(index_data[col], errors='coerce')

        # 重命名列以便统一处理
        index_data.rename(columns={
            '开盘': 'open',
            '最高': 'high', 
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume'
        }, inplace=True)
        
        if index_data.empty:
            logger.warning("中证1000数据预处理后为空。")
            return None

        # 保存缓存
        index_data.to_csv(cache_file)
        logger.info("已更新中证1000的数据缓存。")
        return index_data

    except Exception as e:
        logger.error(f"获取中证1000数据时出错: {e}")
        # 如果API失败，尝试返回旧缓存
        if os.path.exists(cache_file):
            try:
                logger.warning("API请求失败，使用中证1000的旧缓存数据。")
                df = pd.read_csv(cache_file)
                df['时间'] = pd.to_datetime(df['时间'])
                return df.set_index('时间')
            except Exception as cache_err:
                logger.error(f"读取中证1000的旧缓存也失败了: {cache_err}")
        return None

def get_cyb_etf_data(period: str = "5"):
    """
    /**
     * @description 获取创业板ETF分钟线数据，并进行缓存和处理。
     * @param {string} period - K线周期（分钟）。
     * @returns {pd.DataFrame | None} 处理后的分钟线数据。
     */
    """
    cache_file = os.path.join(cache_dir, f"cyb_etf_{period}min.csv")
    
    # 尝试从缓存加载
    if os.path.exists(cache_file):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        if (datetime.now() - file_mod_time).seconds < int(period) * 60:
            try:
                logger.info("使用缓存的创业板ETF数据。")
                df = pd.read_csv(cache_file)
                df['时间'] = pd.to_datetime(df['时间'])
                return df.set_index('时间')
            except Exception as e:
                logger.warning(f"读取创业板ETF缓存失败: {e}, 将重新获取。")

    logger.info("从API获取创业板ETF分钟线数据...")
    try:
        # 计算日期范围
        end_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S")
        
        # 获取创业板ETF分钟线数据
        etf_data = ak.index_zh_a_hist_min_em(
            symbol="159915", 
            period=period, 
            start_date=start_date, 
            end_date=end_date
        )
        
        if etf_data is None or etf_data.empty:
            logger.warning("未能获取到创业板ETF的分钟线数据。")
            return None

        # 数据预处理
        etf_data['时间'] = pd.to_datetime(etf_data['时间'])
        etf_data.set_index('时间', inplace=True)
        
        # 将数据类型转换为数值型
        for col in ['开盘', '最高', '最低', '收盘', '成交量']:
            etf_data[col] = pd.to_numeric(etf_data[col], errors='coerce')

        # 重命名列以便统一处理
        etf_data.rename(columns={
            '开盘': 'open',
            '最高': 'high', 
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume'
        }, inplace=True)
        
        if etf_data.empty:
            logger.warning("创业板ETF数据预处理后为空。")
            return None

        # 保存缓存
        etf_data.to_csv(cache_file)
        logger.info("已更新创业板ETF的数据缓存。")
        return etf_data

    except Exception as e:
        logger.error(f"获取创业板ETF数据时出错: {e}")
        # 如果API失败，尝试返回旧缓存
        if os.path.exists(cache_file):
            try:
                logger.warning("API请求失败，使用创业板ETF的旧缓存数据。")
                df = pd.read_csv(cache_file)
                df['时间'] = pd.to_datetime(df['时间'])
                return df.set_index('时间')
            except Exception as cache_err:
                logger.error(f"读取创业板ETF的旧缓存也失败了: {cache_err}")
        return None

def get_volatility_data(symbol_type: str):
    """
    /**
     * @description 获取波动率数据并缓存
     * @param {string} symbol_type - 标的类型 ('csi1000' 或 'cyb_etf')
     * @returns {dict} 包含历史波动率和分时波动率的字典
     */
    """
    cache_file_min = os.path.join(cache_dir, f"{symbol_type}_min_vix.csv")
    
    # 检查分时波动率缓存是否存在且有效（5分钟内）
    use_cache = False
    if os.path.exists(cache_file_min):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(cache_file_min))
        if (datetime.now() - file_mod_time).seconds < 300:  # 5分钟缓存
            use_cache = True
    
    if use_cache:
        try:
            logger.info(f"使用缓存的{symbol_type}分时波动率数据。")
            min_vol_df = pd.read_csv(cache_file_min, encoding='utf-8-sig')
            return {
                'historical_volatility': None,
                'minute_volatility': min_vol_df
            }
        except Exception as e:
            logger.warning(f"读取{symbol_type}分时波动率缓存失败: {e}, 将重新获取。")
    
    try:
        logger.info(f"从API获取{symbol_type}分时波动率数据...")
        hist_vol_df = None  # 不再获取历史波动率数据
        min_vol_df = None
        
        logger.info(f"跳过{symbol_type}历史波动率获取，将使用技术指标计算")
        
        if symbol_type == 'csi1000':
            try:
                # 中证1000分时波动率
                min_vol_df = ak.index_option_1000index_min_qvix()
                logger.info(f"成功获取{symbol_type}分时波动率数据，形状: {min_vol_df.shape if min_vol_df is not None else 'None'}")
            except Exception as e:
                logger.error(f"获取{symbol_type}分时波动率失败: {e}")
                
        elif symbol_type == 'cyb_etf':
            try:
                # 创业板分时波动率
                min_vol_df = ak.index_option_cyb_min_qvix()
                logger.info(f"成功获取{symbol_type}分时波动率数据，形状: {min_vol_df.shape if min_vol_df is not None else 'None'}")
            except Exception as e:
                logger.error(f"获取{symbol_type}分时波动率失败: {e}")
        else:
            logger.error(f"不支持的标的类型: {symbol_type}")
            return None
        
        # 历史波动率改用技术指标计算，无需缓存
        logger.info(f"历史波动率将使用技术指标计算，无需缓存")
        
        if min_vol_df is not None and not min_vol_df.empty:
            try:
                min_vol_df.to_csv(cache_file_min, index=False, encoding='utf-8-sig')
                logger.info(f"已缓存{symbol_type}分时波动率数据。")
            except Exception as e:
                logger.error(f"保存{symbol_type}分时波动率缓存失败: {e}")
            
        return {
            'historical_volatility': hist_vol_df,
            'minute_volatility': min_vol_df
        }
        
    except Exception as e:
        logger.error(f"获取{symbol_type}波动率数据时出错: {e}")
        # 尝试使用旧分时波动率缓存
        if os.path.exists(cache_file_min):
            try:
                logger.warning(f"API请求失败，使用{symbol_type}的旧分时波动率缓存数据。")
                min_vol_df = pd.read_csv(cache_file_min, encoding='utf-8-sig')
                return {
                    'historical_volatility': None,
                    'minute_volatility': min_vol_df
                }
            except Exception as cache_err:
                logger.error(f"读取{symbol_type}的旧分时波动率缓存也失败了: {cache_err}")
        return None

def calculate_stock_volatility(stock_data: pd.DataFrame, period: int = 20) -> float:
    """
    /**
     * @description 计算标的的历史波动率（年化） - 仅作为备用方案
     * @param {pd.DataFrame} stock_data - 股票数据
     * @param {int} period - 计算周期（默认20日）
     * @returns {float} 年化波动率
     * @note 优先使用API提供的VIX数据，此函数仅在VIX数据不可用时作为备用
     */
    """
    if len(stock_data) < period:
        return 0.0

    # 计算收益率
    returns = stock_data['close'].pct_change().dropna()

    if len(returns) < period:
        return 0.0

    # 计算最近period天的波动率并年化
    recent_returns = returns.tail(period)
    volatility = recent_returns.std() * (252 ** 0.5)  # 年化（假设252个交易日）

    return volatility

def get_daily_data(symbol_type: str):
    """
    /**
     * @description 获取日线数据并缓存
     * @param {string} symbol_type - 标的类型 ('csi1000' 或 'cyb_etf')
     * @returns {pd.DataFrame | None} 日线数据
     */
    """
    cache_file = os.path.join(cache_dir, f"{symbol_type}_daily.csv")
    
    # 检查缓存是否存在且有效（当日有效）
    use_cache = False
    if os.path.exists(cache_file):
        file_mod_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
        if file_mod_time.date() == datetime.now().date():
            use_cache = True
    
    if use_cache:
        try:
            logger.info(f"使用缓存的{symbol_type}日线数据。")
            df = pd.read_csv(cache_file)
            df['日期'] = pd.to_datetime(df['日期'])
            # 重命名列以便统一处理
            df.rename(columns={
                '开盘': 'open',
                '最高': 'high', 
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume'
            }, inplace=True)
            return df.set_index('日期')
        except Exception as e:
            logger.warning(f"读取{symbol_type}日线缓存失败: {e}, 将重新获取。")
    
    logger.info(f"从API获取{symbol_type}日线数据...")
    try:
        # 计算日期范围
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")  # 获取一年数据
        
        if symbol_type == 'csi1000':
            # 获取中证1000日线数据
            daily_data = ak.index_zh_a_hist(symbol="399852", period="daily", start_date=start_date, end_date=end_date)
        elif symbol_type == 'cyb_etf':
            # 获取创业板ETF日线数据
            daily_data = ak.fund_etf_hist_em(symbol="159915", period="daily", start_date=start_date, end_date=end_date)
        else:
            logger.error(f"不支持的标的类型: {symbol_type}")
            return None
        
        if daily_data is None or daily_data.empty:
            logger.warning(f"未能获取到{symbol_type}的日线数据。")
            return None
        
        # 数据预处理
        daily_data['日期'] = pd.to_datetime(daily_data['日期'])
        daily_data.set_index('日期', inplace=True)
        
        # 将数据类型转换为数值型
        numeric_cols = ['开盘', '最高', '最低', '收盘', '成交量'] if symbol_type == 'csi1000' else ['开盘', '最高', '最低', '收盘', '成交量']
        for col in numeric_cols:
            if col in daily_data.columns:
                daily_data[col] = pd.to_numeric(daily_data[col], errors='coerce')
        
        # 重命名列以便统一处理
        daily_data.rename(columns={
            '开盘': 'open',
            '最高': 'high', 
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume'
        }, inplace=True)
        
        # 保存缓存
        daily_data.to_csv(cache_file)
        logger.info(f"已缓存{symbol_type}日线数据。")
        return daily_data
        
    except Exception as e:
        logger.error(f"获取{symbol_type}日线数据时出错: {e}")
        # 如果API失败，尝试返回旧缓存
        if os.path.exists(cache_file):
            try:
                logger.warning(f"API请求失败，使用{symbol_type}的旧日线缓存数据。")
                df = pd.read_csv(cache_file)
                df['日期'] = pd.to_datetime(df['日期'])
                # 重命名列以便统一处理
                df.rename(columns={
                    '开盘': 'open',
                    '最高': 'high', 
                    '最低': 'low',
                    '收盘': 'close',
                    '成交量': 'volume'
                }, inplace=True)
                return df.set_index('日期')
            except Exception as cache_err:
                logger.error(f"读取{symbol_type}的旧日线缓存也失败了: {cache_err}")
        return None

def get_historical_vix_data(symbol: str) -> pd.DataFrame:
    """
    /**
     * @description 使用akshare获取指定指数的历史VIX数据。
     * @param {str} symbol - 标的名称 ('中证1000' 或 '创业板ETF')
     * @returns {pd.DataFrame} 包含历史VIX数据的DataFrame，失败则返回None
     */
    """
    try:
        vix_df = None
        if symbol == '中证1000':
            vix_df = ak.index_option_1000index_qvix()
        elif symbol == '创业板ETF':
            vix_df = ak.index_option_cyb_qvix()
        else:
            logger.warning(f"不支持的标的: {symbol}，无法获取历史VIX")
            return None

        if vix_df is None or vix_df.empty:
            logger.warning(f"获取 {symbol} 历史VIX数据为空")
            return None

        logger.info(f"成功获取 {symbol} 历史VIX数据，共{len(vix_df)}条记录")
        logger.debug(f"原始数据列名: {vix_df.columns.tolist()}")
        logger.debug(f"原始数据样例:\n{vix_df.head(3)}")

        # 统一数据清洗流程
        # 1. 处理日期列
        date_col = None
        for col in ['date', '日期', 'Date', 'DATE']:
            if col in vix_df.columns:
                date_col = col
                break

        if date_col is None:
            logger.error(f"{symbol} VIX数据中未找到日期列")
            return None

        # 2. 处理VIX数值列
        vix_col = None
        for col in ['close', 'Close', 'CLOSE', 'vix', 'VIX', 'qvix', 'QVIX']:
            if col in vix_df.columns:
                vix_col = col
                break

        if vix_col is None:
            logger.error(f"{symbol} VIX数据中未找到波动率数值列")
            return None

        # 3. 数据类型转换和清洗
        try:
            # 转换日期列
            vix_df[date_col] = pd.to_datetime(vix_df[date_col], errors='coerce')

            # 转换VIX数值列 - 修复数值格式问题
            if vix_df[vix_col].dtype == 'object':
                # 如果是字符串类型，先清理非数字字符
                vix_df[vix_col] = vix_df[vix_col].astype(str).str.replace(r'[^\d.-]', '', regex=True)

            # 转换为数值类型
            vix_df[vix_col] = pd.to_numeric(vix_df[vix_col], errors='coerce')

            # 重命名列
            vix_df = vix_df.rename(columns={vix_col: 'vix_close'})

            # 设置日期索引
            vix_df = vix_df.set_index(date_col)

            # 删除无效数据
            vix_df = vix_df.dropna(subset=['vix_close'])

            # 过滤异常值（VIX通常在0-100之间）
            vix_df = vix_df[(vix_df['vix_close'] >= 0) & (vix_df['vix_close'] <= 100)]

            if vix_df.empty:
                logger.warning(f"{symbol} VIX数据清洗后为空")
                return None

            logger.info(f"成功清洗 {symbol} VIX数据，有效记录: {len(vix_df)}条")
            logger.debug(f"清洗后数据样例:\n{vix_df.head(3)}")

            return vix_df

        except Exception as e:
            logger.error(f"清洗 {symbol} VIX数据时出错: {e}")
            return None

    except Exception as e:
        logger.error(f"获取 {symbol} 历史VIX数据时发生严重错误: {e}", exc_info=True)
        return None

def get_pcr_data():
    """
    /**
     * @description 获取认沽/认购持仓比数据
     * @returns {dict} 包含上交所、深交所和中证1000期权PCR数据的字典
     */
    """
    try:
        today = datetime.now().strftime("%Y%m%d")
        
        pcr_data = {}
        
        # 上交所期权数据
        try:
            sse_data = ak.option_daily_stats_sse(date=today)
            if not sse_data.empty:
                pcr_data['sse'] = sse_data
        except Exception as e:
            logger.warning(f"获取上交所期权数据失败: {e}")
            
        # 深交所期权数据
        try:
            szse_data = ak.option_daily_stats_szse(date=today)
            if not szse_data.empty:
                pcr_data['szse'] = szse_data
        except Exception as e:
            logger.warning(f"获取深交所期权数据失败: {e}")
            
        # 中证1000期权数据
        try:
            # 获取当前月份的期权合约
            current_month = datetime.now().strftime("%y%m")
            csi1000_data = ak.option_finance_board(symbol="中证1000股指期权", end_month=current_month)
            if not csi1000_data.empty:
                pcr_data['csi1000'] = csi1000_data
        except Exception as e:
            logger.warning(f"获取中证1000期权数据失败: {e}")
            
        return pcr_data
        
    except Exception as e:
        logger.error(f"获取PCR数据时出错: {e}")
        return {}

# --- 技术指标计算模块 ---

def calculate_technical_indicators(data: pd.DataFrame) -> pd.DataFrame:
    """
    /**
     * @description 计算所有技术指标
     * @param {pd.DataFrame} data - 原始价格数据
     * @returns {pd.DataFrame} 包含技术指标的数据
     */
    """
    df = data.copy()
    
    # 1. 多均线系统
    df['EMA8'] = df['close'].ewm(span=8, adjust=False).mean()
    df['EMA21'] = df['close'].ewm(span=21, adjust=False).mean()
    df['EMA55'] = df['close'].ewm(span=55, adjust=False).mean()
    df['EMA125'] = df['close'].ewm(span=125, adjust=False).mean()
    
    # 2. KDJ随机指标（14周期）
    low_list = df['low'].rolling(window=14, min_periods=1).min()
    high_list = df['high'].rolling(window=14, min_periods=1).max()
    rsv = (df['close'] - low_list) / (high_list - low_list) * 100
    df['K'] = rsv.ewm(com=2, adjust=False).mean()  # SMA(RSV,3,1) 近似
    df['D'] = df['K'].ewm(com=2, adjust=False).mean()  # SMA(K,3,1) 近似
    df['J'] = 3 * df['K'] - 2 * df['D']
    
    # 3. MACD指标
    df['EMA12'] = df['close'].ewm(span=12, adjust=False).mean()
    df['EMA26'] = df['close'].ewm(span=26, adjust=False).mean()
    df['DIF'] = df['EMA12'] - df['EMA26']
    df['DEA'] = df['DIF'].ewm(span=9, adjust=False).mean()
    df['MACD'] = (df['DIF'] - df['DEA']) * 2
    
    # 4. 威廉指标变种（核心创新）
    N = 19
    df['VAR1'] = df['high'].rolling(window=N, min_periods=1).max()  # HHV(HIGH,19)
    df['VAR2'] = df['low'].rolling(window=N, min_periods=1).min()   # LLV(LOW,19)
    
    # 计算威廉指标基础值
    williams_base = (df['close'] - df['VAR2']) / (df['VAR1'] - df['VAR2'])
    williams_base = williams_base.fillna(0.5)  # 处理除零情况
    
    # ZLS: 21日EMA平滑后减0.5 (长期线)
    df['ZLS'] = williams_base.ewm(span=21, adjust=False).mean() - 0.5
    
    # CZX: 5日EMA平滑后减0.5 (短期线)  
    df['CZX'] = williams_base.ewm(span=5, adjust=False).mean() - 0.5
    
    # HLB: 两线差值
    df['HLB'] = df['CZX'] - df['ZLS']
    
    # 5. 成交量分析
    df['VOL_MA10'] = df['volume'].rolling(window=10, min_periods=1).mean()
    
    return df

# --- 信号检测模块 ---

def detect_signals(data: pd.DataFrame, symbol: str, volatility_data: dict = None, daily_data: pd.DataFrame = None) -> tuple:
    """
    /**
     * @description 检测交易信号（双重确认升级版策略）
     * @param {pd.DataFrame} data - 包含技术指标的数据
     * @param {string} symbol - 标的代码
     * @param {dict} volatility_data - 波动率数据字典
     * @returns {tuple} 包含市场数据字典和信号检测标志的元组
     */
    """
    if len(data) < 3:
        logger.warning(f"{symbol} 数据点不足，无法生成信号。")
        return None, False
        
    # 获取当前和历史数据点
    current_row = data.iloc[-1]
    prev_row = data.iloc[-2] if len(data) >= 2 else current_row

    # --- 威廉指标信号检测 ---
    williams_c_signal = False  # 超卖反弹信号
    williams_p_signal = False  # 超买回调信号
    
    # C信号: CZX上穿ZLS且ZLS<0.1（超卖反弹）
    if (current_row['CZX'] > current_row['ZLS'] and 
        prev_row['CZX'] <= prev_row['ZLS'] and 
        current_row['ZLS'] < 0.1):
        williams_c_signal = True
        logger.info(f"{symbol}: 威廉指标C信号 - 超卖反弹 (ZLS={current_row['ZLS']:.3f})")
    
    # P信号: ZLS上穿CZX且ZLS>0.25（超买回调）
    if (current_row['ZLS'] > current_row['CZX'] and 
        prev_row['ZLS'] <= prev_row['CZX'] and 
        current_row['ZLS'] > 0.25):
        williams_p_signal = True
        logger.info(f"{symbol}: 威廉指标P信号 - 超买回调 (ZLS={current_row['ZLS']:.3f})")

    # --- 双重确认升级版信号逻辑 ---
    signal_type = "无信号"
    signal_strength = 0
    volume_confirmation = False
    williams_confirmation = ""
    
    # 成交量确认条件（异常放量确认：当前成交量 > 前10根K线均值 × 1.3）
    if current_row['volume'] > current_row['VOL_MA10'] * 1.3:
        volume_confirmation = True
        logger.info(f"{symbol}: 检测到成交量异常放大 (当前: {current_row['volume']:.0f}, 10日均量: {current_row['VOL_MA10']:.0f})")

    # 1. 多头进场信号（强度3-5）
    # EMA8上穿EMA21 + 当前收盘>EMA8
    if (current_row['EMA8'] > current_row['EMA21'] and 
        prev_row['EMA8'] < prev_row['EMA21'] and 
        current_row['close'] > current_row['EMA8']):
        signal_type = "多头进场信号"
        signal_strength = 3
        if volume_confirmation:
            signal_strength += 1
        if williams_c_signal:  # 威廉指标确认
            signal_strength += 1
            williams_confirmation = "威廉C信号确认"

    # 2. 空头进场信号（强度3-5）
    # EMA8下穿EMA21 + 当前收盘<EMA8
    elif (current_row['EMA21'] > current_row['EMA8'] and 
          prev_row['EMA8'] > prev_row['EMA21'] and 
          current_row['close'] < current_row['EMA8']):
        signal_type = "空头进场信号"
        signal_strength = 3
        if volume_confirmation:
            signal_strength += 1
        if williams_p_signal:  # 威廉指标确认
            signal_strength += 1
            williams_confirmation = "威廉P信号确认"

    # 3. 多头预警信号（强度2-3）
    # 收盘价介于EMA8和EMA21之间 + J线上穿K线
    elif (current_row['close'] > min(current_row['EMA8'], current_row['EMA21']) and 
          current_row['close'] < max(current_row['EMA8'], current_row['EMA21']) and 
          current_row['J'] > current_row['K'] and 
          prev_row['J'] < prev_row['K']):
        signal_type = "多头预警信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        if williams_c_signal:  # 威廉指标确认可升级为进场信号
            signal_type = "多头进场信号(威廉确认)"
            signal_strength += 1
            williams_confirmation = "威廉C信号升级"

    # 4. 空头预警信号（强度2-3）
    # 收盘价介于EMA8和EMA21之间 + K线上穿J线
    elif (current_row['close'] > min(current_row['EMA8'], current_row['EMA21']) and 
          current_row['close'] < max(current_row['EMA8'], current_row['EMA21']) and 
          current_row['K'] > current_row['J'] and 
          prev_row['K'] < prev_row['J']):
        signal_type = "空头预警信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        if williams_p_signal:  # 威廉指标确认可升级为进场信号
            signal_type = "空头进场信号(威廉确认)"
            signal_strength += 1
            williams_confirmation = "威廉P信号升级"

    # 5. 纯威廉指标信号（强度2-3）
    elif williams_c_signal:
        signal_type = "威廉超卖反弹信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        williams_confirmation = "纯威廉C信号"
    elif williams_p_signal:
        signal_type = "威廉超买回调信号"
        signal_strength = 2
        if volume_confirmation:
            signal_strength += 1
        williams_confirmation = "纯威廉P信号"

    # 6. 成交量异常信号（强度1）
    elif volume_confirmation:
        signal_type = "成交量异常信号"
        signal_strength = 1

    # 判断是否有信号（最低触发阈值：信号强度≥2）
    signal_detected = signal_strength >= 2

    # 优先使用VIX波动率数据，不再自己计算
    current_vix = None
    historical_vix = None
    vol_percentile = 0.5  # 默认值
    stock_volatility = 0.0  # 备用计算波动率，仅在VIX完全不可用时使用

    # 1. 首先尝试获取分时VIX波动率数据
    if volatility_data and volatility_data.get('minute_volatility') is not None:
        try:
            min_vix_df = volatility_data['minute_volatility']
            if not min_vix_df.empty:
                logger.debug(f"{symbol} 分时VIX数据列名: {min_vix_df.columns.tolist()}")
                logger.debug(f"{symbol} 分时VIX数据样例:\n{min_vix_df.head(3)}")

                # 尝试多种可能的列名来获取VIX数据
                vix_columns = ['qvix', 'QVIX', 'vix', 'VIX', 'close', 'Close', 'CLOSE']
                vix_value = None

                for col in vix_columns:
                    if col in min_vix_df.columns:
                        valid_data = min_vix_df.dropna(subset=[col])
                        if not valid_data.empty:
                            vix_value = valid_data.iloc[-1][col]
                            logger.info(f"{symbol} 从列 '{col}' 获取到分时VIX: {vix_value}")
                            break

                # 如果没有找到命名列，尝试使用数值列
                if vix_value is None and len(min_vix_df.columns) > 1:
                    numeric_cols = min_vix_df.select_dtypes(include=[np.number]).columns
                    if len(numeric_cols) > 0:
                        col = numeric_cols[-1]  # 使用最后一个数值列
                        valid_data = min_vix_df.dropna(subset=[col])
                        if not valid_data.empty:
                            vix_value = valid_data.iloc[-1][col]
                            logger.info(f"{symbol} 从数值列 '{col}' 获取到分时VIX: {vix_value}")

                if vix_value is not None and pd.notna(vix_value):
                    current_vix = float(vix_value)
                    logger.info(f"{symbol} 成功获取当前VIX波动率: {current_vix:.2f}%")
                else:
                    logger.warning(f"{symbol} 分时VIX数据全部为空值或无效")
        except Exception as e:
            logger.warning(f"解析{symbol}分时波动率数据失败: {e}")

    # 2. 获取历史VIX数据并计算百分位
    if current_vix is not None:
        try:
            historical_vix_data = get_historical_vix_data(symbol)

            if historical_vix_data is not None and not historical_vix_data.empty:
                # 计算过去20日VIX平均值
                historical_vix_mean = historical_vix_data.tail(20)['vix_close'].mean()
                historical_vix = historical_vix_data.iloc[-1]['vix_close']  # 最新的历史VIX

                # 计算当前VIX相对于历史平均的百分位
                vol_percentile = current_vix / historical_vix_mean if historical_vix_mean > 0 else 1.0

                logger.info(f"{symbol} 当前VIX: {current_vix:.2f}%, 历史20日平均VIX: {historical_vix_mean:.2f}%")
                logger.info(f"{symbol} VIX波动率百分位(当前/历史平均): {vol_percentile*100:.1f}%")
            else:
                logger.warning(f"{symbol} 无法获取历史VIX数据，使用当前VIX作为历史值")
                historical_vix = current_vix
                vol_percentile = 1.0
        except Exception as e:
            logger.warning(f"计算{symbol}历史VIX百分位失败: {e}")
            historical_vix = current_vix
            vol_percentile = 1.0

    # 3. 如果VIX数据完全不可用，才使用计算的股票波动率作为最后备选
    if current_vix is None:
        logger.warning(f"{symbol} 无法获取VIX数据，使用计算波动率作为备选")
        stock_volatility = calculate_stock_volatility(data, period=20)
        current_vix = stock_volatility * 100  # 转换为百分比格式
        historical_vix = current_vix
        vol_percentile = 1.0
        logger.info(f"{symbol} 使用计算波动率: {current_vix:.2f}%")
    
    # 构建市场数据
    market_data = {
        'symbol': symbol,
        'timestamp': current_row.name.strftime('%Y-%m-%d %H:%M:%S'),
        'price': current_row['close'],
        'signal_type': signal_type,
        'signal_strength': signal_strength,
        'volume_confirmation': volume_confirmation,
        'williams_confirmation': williams_confirmation,
        # 技术指标数据
        'EMA8': current_row['EMA8'],
        'EMA21': current_row['EMA21'],
        'EMA55': current_row['EMA55'],
        'EMA125': current_row['EMA125'],
        'K_value': current_row['K'],
        'D_value': current_row['D'],
        'J_value': current_row['J'],
        'DIF': current_row['DIF'],
        'DEA': current_row['DEA'],
        'MACD': current_row['MACD'],
        'volume': current_row['volume'],
        'vol_ma10': current_row['VOL_MA10'],
        'vol_ratio': current_row['volume'] / current_row['VOL_MA10'] if current_row['VOL_MA10'] > 0 else 0,
        # 威廉指标数据
        'ZLS': current_row['ZLS'],
        'CZX': current_row['CZX'],
        'HLB': current_row['HLB'],
        'williams_c_signal': williams_c_signal,
        'williams_p_signal': williams_p_signal,
        # 波动率数据（优先使用VIX数据）
        'stock_volatility': stock_volatility,  # 仅作为备用
        'current_vix': current_vix,  # 当前VIX波动率（主要使用）
        'historical_vix': historical_vix,  # 历史VIX波动率
        'vol_percentile': f"{vol_percentile*100:.1f}%",  # VIX百分位
        # 趋势分析
        'trend_short': "上涨" if current_row['EMA8'] > current_row['EMA21'] else "下跌",
        'trend_medium': "上涨" if current_row['EMA21'] > current_row['EMA55'] else "下跌",
        'trend_long': "上涨" if current_row['EMA55'] > current_row['EMA125'] else "下跌",
        'signal': signal_type
    }

    if signal_detected:
        confirmation_info = f" ({williams_confirmation})" if williams_confirmation else ""
        logger.info(f"{symbol} 检测到信号: {signal_type} (强度: {signal_strength}){confirmation_info}")
    
    return market_data, signal_detected

# --- 策略分析模块 ---

def analyze_symbol_data(symbol: str, symbol_info: dict):
    """
    /**
     * @description 分析单个A股标的数据并生成交易信号
     * @param {string} symbol - 标的代码
     * @param {dict} symbol_info - 标的信息字典
     * @returns {tuple} 包含市场数据的字典和信号检测标志的元组
     */
     """
    logger.info(f"开始分析 {symbol_info['name']} 数据...")
    
    # 获取分钟线数据
    if symbol == 'csi1000':
        stock_data = get_csi1000_data(period=str(CONFIG['period']))
    elif symbol == 'cyb_etf':
        stock_data = get_cyb_etf_data(period=str(CONFIG['period']))
    else:
        logger.error(f"不支持的标的: {symbol}")
        return None, False
    
    if stock_data is None or stock_data.empty:
        logger.error(f"无法获取 {symbol_info['name']} 数据，跳过分析。")
        return None, False

    # 获取日线数据
    daily_data = get_daily_data(symbol)
    
    # 获取波动率数据
    volatility_data = get_volatility_data(symbol)
    
    # 计算技术指标
    stock_data = calculate_technical_indicators(stock_data)
    
    # 检测信号（传入波动率数据和日线数据）
    market_data, signal_detected = detect_signals(stock_data, symbol_info['name'], volatility_data, daily_data)
    
    return market_data, signal_detected

# --- 任务调度模块 ---

def is_a_stock_trading_hours(test_mode=False):
    """
    /**
     * @description 判断当前是否在A股交易时段（北京时间 9:30-11:30 & 13:00-15:00）
     * @param {boolean} test_mode - 测试模式，如果为True则忽略交易时间限制
     * @returns {boolean} 如果是交易时间则返回True
     */
    """
    if test_mode:
        logger.info("测试模式：忽略交易时间限制")
        return True
        
    now = datetime.now()
    
    # 只在工作日运行
    if now.weekday() >= 5:
        logger.info("当前为周末，非交易日")
        return False
    
    # A股交易时段: 9:30-11:30 & 13:00-15:00
    morning_start = now.replace(hour=9, minute=30, second=0, microsecond=0).time()
    morning_end = now.replace(hour=11, minute=30, second=0, microsecond=0).time()
    afternoon_start = now.replace(hour=13, minute=0, second=0, microsecond=0).time()
    afternoon_end = now.replace(hour=15, minute=0, second=0, microsecond=0).time()
    
    current_time = now.time()
    is_trading = ((morning_start <= current_time <= morning_end) or 
                  (afternoon_start <= current_time <= afternoon_end))
    
    if not is_trading:
        logger.info(f"当前时间 {current_time} 不在交易时段")
    
    return is_trading

def send_notification(message: str):
    """
    /**
     * @description 发送通知（简化版本，可扩展为邮件或企业微信）
     * @param {string} message - 通知消息
     */
    """
    try:
        # 这里可以集成邮件或企业微信通知
        # 目前只记录到日志
        logger.info(f"通知消息: {message}")
        print(f"\n=== 交易信号通知 ===\n{message}\n===================")
    except Exception as e:
        logger.error(f"发送通知失败: {e}")

def job(test_mode=False, force_run=False):
    """
    /**
     * @description 定时执行的任务，分析所有配置的标的
     * @param {boolean} test_mode - 测试模式，忽略交易时间限制
     * @param {boolean} force_run - 强制运行一次
     */
    """
    if not test_mode and not force_run and not is_a_stock_trading_hours():
        logger.info("当前非A股交易时段，跳过运行。")
        return
        
    start_time = time.time()
    logger.info("开始A股期权策略定时任务...")
    
    for symbol, symbol_info in CONFIG['symbols'].items():
        try:
            market_data, signal_detected = analyze_symbol_data(symbol, symbol_info)
            if signal_detected and market_data:
                logger.info(f"为 {symbol_info['name']} 检测到信号，正在获取AI导师分析...")
                master_analysis = get_master_analysis(market_data)
                logger.info(f"AI导师分析结果 for {symbol_info['name']}:\n{master_analysis}")
                # 发送通知
                notification_message = f"--- AI导师A股期权分析: {symbol_info['name']} ---\n{master_analysis}"
                send_notification(notification_message)
            elif market_data:
                logger.info(f"{symbol_info['name']} 未检测到明确交易信号。")
        except Exception as e:
            logger.error(f"分析 {symbol_info['name']} 数据时出错: {e}", exc_info=True)
            
    elapsed_time = time.time() - start_time
    logger.info(f"任务完成，耗时: {elapsed_time:.2f}秒")

def run_scheduler(test_mode=False):
    """
    /**
     * @description 启动定时任务调度器
     * @param {boolean} test_mode - 测试模式
     */
    """
    logger.info("启动A股期权策略定时任务调度器...")
    
    schedule.every(int(CONFIG['period'])).minutes.do(job, test_mode=test_mode)
    
    logger.info("立即执行一次任务...")
    job(test_mode=test_mode, force_run=True)
    
    logger.info(f"定时任务已设置，每{CONFIG['period']}分钟运行一次。")
    
    while True:
        try:
            schedule.run_pending()
            time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到中断信号，退出程序。")
            break
        except Exception as e:
            logger.error(f"调度器出错: {str(e)}")
            send_notification(f"A股策略调度器出错: {str(e)}")
            time.sleep(60)

def init():
    """
    /**
     * @description 初始化程序，加载配置和检查API密钥
     */
    """
    # 加载 .env 文件中的环境变量
    if not load_dotenv():
        logger.warning(".env 文件未找到。请确保在项目根目录下创建了 .env 文件，并已设置 DEEPSEEK_API_KEY。")
        logger.warning("例如: DEEPSEEK_API_KEY=\"your_key_here\"")

    load_master_agent_prompt()
    
    if not os.getenv("DEEPSEEK_API_KEY"):
        logger.error("关键错误: DEEPSEEK_API_KEY 环境变量未设置。程序无法继续。")
        logger.info("请在终端使用 'export DEEPSEEK_API_KEY=\"your_key_here\"' 命令设置。")
        sys.exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='A股期权策略跟踪系统')
    parser.add_argument('--test-mode', action='store_true', help='测试模式：忽略交易时间限制')
    parser.add_argument('--force-run', action='store_true', help='强制运行一次分析')
    parser.add_argument('--run-once', action='store_true', help='运行一次性分析（兼容旧参数）')
    
    args = parser.parse_args()
    
    try:
        init()
        
        if not MASTER_AGENT_PROMPT:
            logger.error("AI导师配置未能加载，程序退出。")
            sys.exit(1)

        if args.run_once or args.force_run:
            logger.info("运行一次性分析...")
            job(test_mode=args.test_mode, force_run=True)
            logger.info("一次性分析完成。")
        else:
            run_scheduler(test_mode=args.test_mode)
            
    except Exception as e:
        error_msg = f"程序主流程运行出错: {str(e)}"
        logger.error(error_msg, exc_info=True)
        try:
            send_notification(f"A股策略程序运行出错: {str(e)}")
        except Exception as notify_e:
            logger.error(f"发送错误通知也失败了: {notify_e}")